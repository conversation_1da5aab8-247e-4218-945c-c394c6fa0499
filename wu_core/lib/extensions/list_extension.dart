extension ListExtension<T> on List<T> {
  void toggle(T value) {
    if (contains(value)) {
      remove(value);
    } else {
      add(value);
    }
  }

  int exists(List<T> tests) {
    var ret = 0;
    for (var test in tests) {
      if (contains(test)) ret++;
    }
    return ret;
  }

  Map<T, int> count() {
    final ret = <T, int>{};
    for (var item in this) {
      ret[item] = (ret[item] ?? 0) + 1;
    }
    return ret;
  }
}
