import 'package:intl/intl.dart';

extension DateTimeExtension on DateTime {
  DateTime date() {
    return DateTime(year, month, day);
  }

  int gzhour() {
    return (hour - 1) ~/ 2 * 2 + 1;
  }

  bool isSameDate(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }

  bool between(DateTime sDate, DateTime eDate, {bool dateOnly = false}) {
    if (dateOnly) {
      return date().compareTo(sDate.date()) >= 0 && date().compareTo(eDate) <= 0;
    }
    return isAfter(sDate) && isBefore(eDate);
  }

  String dateString() => DateFormat("yyyyMMdd").format(this);
  String md() => DateFormat("MM-dd").format(this);
  String ymd() => DateFormat("yyyy-MM-dd").format(this);
  String ymdhm() => DateFormat("yyyy-MM-dd HH:mm").format(this);
}
