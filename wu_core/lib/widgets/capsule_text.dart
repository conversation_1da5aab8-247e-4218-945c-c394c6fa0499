import 'package:flutter/material.dart';

/// 膠囊文字
class CapsuleText extends StatelessWidget {
  final String leftText;
  final String rightText;
  final Color? leftBackColor;
  final Color? leftForeColor;
  final Color? rightBackColor;
  final Color? rightForeColor;
  final double? leftWidth;
  final BoxDecoration? decoration;
  const CapsuleText(
      {super.key,
      required this.leftText,
      required this.rightText,
      this.leftBackColor,
      this.leftForeColor,
      this.decoration,
      this.rightBackColor,
      this.rightForeColor,
      this.leftWidth});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    var workDecoration = decoration ?? BoxDecoration();
    workDecoration = workDecoration.copyWith(color: rightBackColor);

    return Container(
      decoration: workDecoration,
      clipBehavior: Clip.antiAlias,
      child: Row(
        children: [
          Container(
            width: leftWidth,
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            color: leftBackColor ?? colorScheme.secondaryContainer,
            child: Text(
              leftText,
              style: TextStyle(color: leftForeColor ?? colorScheme.onSecondaryContainer),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            child: Text(rightText, style: TextStyle(color: rightForeColor ?? colorScheme.onSurface)),
          ),
        ],
      ),
    );
  }
}
