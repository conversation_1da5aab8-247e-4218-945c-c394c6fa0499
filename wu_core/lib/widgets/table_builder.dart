import 'package:flutter/material.dart';

typedef CellBuilder = Widget Function(int index);

/// 表格建構器，使用表格建構類似 GridView 的效果
class TableBuilder extends StatelessWidget {
  final int itemCount;
  final int columnCount;
  final CellBuilder cellBuilder;
  final TableBorder? border;

  const TableBuilder({
    super.key,
    required this.itemCount,
    required this.cellBuilder,
    required this.columnCount,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    return Table(
        border: border,
        children: List.generate(itemCount ~/ columnCount + 1, (rowIndex) {
          return TableRow(
              children: List.generate(columnCount, (colIndex) {
            final itemIndex = rowIndex * columnCount + colIndex;
            if (itemIndex >= itemCount) return Container();
            return cellBuilder(itemIndex);
          }));
        }));
  }
}
