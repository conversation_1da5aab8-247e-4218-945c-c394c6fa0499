import 'package:flip_card/flip_card.dart';
import 'package:flutter/material.dart';

export 'package:flip_card/flip_card.dart';
export 'package:flip_card/flip_card_controller.dart';

typedef CardBuilder = Widget Function(int index, double cardWidth);

class CardDrewer extends StatefulWidget {
  final int cardCount;
  final CardBuilder frontBuilder;
  final CardBuilder backBuilder;
  final Function(int index) onFlipDone;
  final bool Function(int index)? enabledFlip;
  final CardSide Function(int index)? cardSide;
  final int crossAxisCount;
  final double spacing;
  final double runSpacing;
  final int flipSpeed;
  final FlipDirection flipDirection;
  final bool redraw;
  final bool enabled;
  const CardDrewer(
      {required this.cardCount,
      required this.frontBuilder,
      required this.backBuilder,
      required this.onFlipDone,
      this.enabledFlip,
      this.cardSide,
      this.crossAxisCount = 4,
      this.spacing = 8,
      this.runSpacing = 8,
      this.flipSpeed = 500,
      this.flipDirection = FlipDirection.HORIZONTAL,
      this.redraw = false,
      this.enabled = true,
      super.key});

  @override
  State<CardDrewer> createState() => _CardDrewerState();
}

class _CardDrewerState extends State<CardDrewer> {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, con) {
      final cardWidth = ((con.maxWidth - widget.spacing * (widget.crossAxisCount - 1)) / widget.crossAxisCount);
      // print('${con.maxWidth},$cardWidth');
      return Wrap(
        spacing: widget.spacing,
        runSpacing: widget.runSpacing,
        children: List.generate(
          widget.cardCount,
          (index) => FlipCard(
            key: widget.redraw ? UniqueKey() : null, // 讓牌面每次都重置
            speed: widget.flipSpeed,
            direction: widget.flipDirection,
            side: widget.cardSide?.call(index) ?? CardSide.FRONT,
            front: widget.frontBuilder(index, cardWidth),
            back: widget.backBuilder(index, cardWidth),
            onFlipDone: (isFront) => widget.onFlipDone(index),
            flipOnTouch: (widget.enabledFlip?.call(index) ?? true) && widget.enabled,
          ),
        ),
      );
    });
  }
}
