import 'package:flutter/material.dart';
import 'package:wu_core/wu_extensions.dart';

class SolarHeader extends StatelessWidget {
  final DateTime solar;
  final Color backColor;
  final Color textColor;

  const SolarHeader({super.key, required this.solar, required this.backColor, required this.textColor});

  @override
  Widget build(BuildContext context) {
    var text = solar.ymd();
    if (solar.isSameDate(DateTime.now())) text = "TODAY";

    return Container(
      decoration: BoxDecoration(color: backColor),
      padding: EdgeInsets.symmetric(vertical: 2, horizontal: 8),
      child: Text(text, style: TextStyle(color: textColor)),
    );
  }
}
