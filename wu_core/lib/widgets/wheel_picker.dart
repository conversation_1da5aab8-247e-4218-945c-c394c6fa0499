import 'package:flutter/cupertino.dart';

class WheelItem {
  final Widget display;
  final dynamic value;

  WheelItem(this.display, this.value);
}

typedef OnSelectedChanged = void Function(dynamic value);

class WheelPicker extends StatelessWidget {
  final List<WheelItem> items;
  final OnSelectedChanged? onSelectedItemChanged;
  final Color? backgroundColor;
  final double? width;
  final double? height;

  const WheelPicker({
    super.key,
    required this.items,
    this.onSelectedItemChanged,
    this.backgroundColor,
    this.width,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CupertinoPicker(
        itemExtent: 32.0,
        looping: true,
        backgroundColor: backgroundColor,
        onSelectedItemChanged: (index) => onSelectedItemChanged?.call(items[index].value),
        children: items.map((e) => e.display).toList(),
      ),
    );
  }
}
