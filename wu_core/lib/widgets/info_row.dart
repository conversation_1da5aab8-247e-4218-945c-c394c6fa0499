import 'package:flutter/material.dart';

class InfoRow extends StatelessWidget {
  final String left;
  final String right;
  final double textScale;
  const InfoRow({super.key, required this.left, required this.right, this.textScale = 1.0});

  @override
  Widget build(BuildContext context) {
    final scaler = TextScaler.linear(textScale);
    return Text.rich(
      textScaler: scaler,
      TextSpan(
        children: [
          TextSpan(text: left, style: const TextStyle(fontWeight: FontWeight.bold)),
          TextSpan(text: right),
        ],
      ),
    );
  }
}
