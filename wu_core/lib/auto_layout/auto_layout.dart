import 'package:flutter/material.dart';
import 'auto_frame.dart';

export 'auto_frame.dart' show SizeLevel;

/// 響應式佈局元件
///
/// 此元件用於根據螢幕寬度自動選擇最適合的佈局版本。
/// 支援三種不同的佈局尺寸：
/// - L（電腦版）：適用於桌面電腦
/// - M（平板版）：適用於平板裝置
/// - S（手機版）：適用於手機裝置
///
/// 佈局選擇邏輯：
/// 1. 當螢幕寬度大於 L 斷點時，直接使用 L 版佈局
/// 2. 其他情況下，根據 Frame 提供的 widthLevel 選擇對應的佈局
/// 3. 如果指定的佈局不存在，會依序嘗試使用較大尺寸的佈局作為備選
///
/// 使用範例：
/// ```dart
/// ResponsiveLayout(
///   buildContentL: (context) => DesktopLayout(),    // 電腦版佈局
///   buildContentM: (context) => TabletLayout(),     // 平板版佈局（選填）
///   buildContentS: (context) => MobileLayout(),     // 手機版佈局（選填）
/// )
/// ```
class AutoLayout extends StatelessWidget {
  final Widget Function(BuildContext, SizeLevel) buildContentL;
  final Widget Function(BuildContext, SizeLevel) buildContentS;

  const AutoLayout({super.key, required this.buildContentL, required this.buildContentS});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final sizeLevel =
        width < 600
            ? SizeLevel.S
            : width < 1200
            ? SizeLevel.M
            : SizeLevel.L;

    return width < 600 ? buildContentS(context, sizeLevel) : buildContentL(context, sizeLevel);
  }
}

/// 手機版佈局基礎類別
///
/// 提供手機版佈局的基本結構，包含 SafeArea 包裝以確保內容不會被系統 UI 遮擋。
/// 子類別需要實作 buildMobileContent 方法來提供具體的佈局內容。
///
/// 手機版特別考量：
/// - 建議重新設計操作流程，使其更適合觸控操作
/// - 多欄佈局建議改用 TabBarView
/// - 單欄佈局建議改用 ListView
///
/// 使用範例：
/// ```dart
/// class MyMobileLayout extends MobileLayoutBase {
///   @override
///   Widget buildMobileContent(BuildContext context) {
///     return Scaffold(
///       // 實作手機版佈局內容
///       body: Padding(
///         padding: EdgeInsets.symmetric(horizontal: SizeLevel.S.margin),
///         child: YourWidget(), // 您的元件可以在這裡控制版面呈現，同時保持規範的邊界寬度
///       ),
///     );
///   }
/// }
/// ```
abstract class MobileLayoutBase extends StatelessWidget {
  const MobileLayoutBase({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(child: buildMobileContent(context, SizeLevel.S));
  }

  /// 子類需要實現此方法來提供行動版內容
  Widget buildMobileContent(BuildContext context, SizeLevel sizeLevel);
}

/// 通用版佈局基礎類別
///
/// 使用 Frame 元件作為基礎容器，提供以下功能：
/// 1. 自動限制頁面最大寬度，確保在大螢幕上的最佳顯示效果
/// 2. 提供響應式佈局所需的尺寸等級（sizeLevel）和實際寬度（frameWidth）
/// 3. 內建頁面捲動功能
///
/// 佈局層級說明：
/// - 第一層（頁面）：可直接使用 sizeLevel 和 frameWidth 進行佈局
/// - 第二層（多欄式）：需使用 LayoutBuilder 取得實際可用寬度
/// - 建議使用 SizeLevel.gutter 作為欄位間距參考值
///
/// 使用範例：
/// ```dart
/// class MyCommonLayout extends CommonLayoutBase {
///   @override
///   Widget buildCommonContent(BuildContext context, SizeLevel sizeLevel, double frameWidth) {
///     return Column(
///       children: [
///         // 實作您的通用版佈局內容
///         Padding(
///           padding: EdgeInsets.symmetric(horizontal: sizeLevel.margin),
///           child: YourWidget(), // 您的元件可以在這裡控制版面呈現，同時保持規範的邊界寬度
///         ),
///       ],
///     );
///   }
/// }
/// ```
abstract class CommonLayoutBase extends StatelessWidget {
  const CommonLayoutBase({super.key});

  @override
  Widget build(BuildContext context) {
    return AutoFrame(builder: (context, sizeLevel, frameWidth) => buildCommonContent(context, sizeLevel, frameWidth));
  }

  /// 子類需要實現此方法來提供通用版內容
  Widget buildCommonContent(BuildContext context, SizeLevel sizeLevel, double frameWidth);
}
