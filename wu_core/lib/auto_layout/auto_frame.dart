// ignore_for_file: constant_identifier_names

import 'dart:developer' as console;

import 'package:flutter/material.dart';

/// 響應式佈局系統配置
///
/// 系統會根據不同螢幕寬度自動調整邊距和間距：
enum SizeLevel {
  S(breakpoint: 450, gutter: 12, margin: 16), // 手機版（大螢幕手機）
  M(breakpoint: 800, gutter: 12, margin: 24), // 平板版（直向）
  L(breakpoint: 1440, gutter: 16, margin: 32); // 電腦版（桌面）

  final double breakpoint;
  final double gutter;
  final double margin;

  const SizeLevel({required this.breakpoint, required this.gutter, required this.margin});
}

/// 框架建構器函數類型定義
///
/// 參數說明：
/// - context: 建構上下文
/// - sizeLevel: 當前螢幕尺寸等級
/// - frameWidth: 框架實際可用寬度，理論上用不到
typedef FrameBuilder = Widget Function(BuildContext context, SizeLevel sizeLevel, double frameWidth);

/// 響應式框架元件
///
/// 此元件提供一個自適應的佈局容器，能夠根據不同螢幕尺寸自動調整內容區域的寬度和間距。
///
/// 主要特點：
/// 1. 自動響應式佈局：依據螢幕寬度自動選擇合適的尺寸等級
/// 2. 統一的間距系統：提供一致的邊距和間距設定
/// 3. 內容寬度限制：確保在大螢幕上內容不會過寬
/// 4. 可選的捲動支援：支援單向捲動
///
/// 尺寸等級設定：
/// - S (450px)：適用於大螢幕手機
/// - M (800px)：適用於平板直式
/// - L (1440px)：適用於桌面電腦
///
/// 使用範例：
/// ```dart
/// Frame(
///   backgroundColor: Colors.white,
///   alignment: Alignment.center,
///   isScrollable: true,
///   builder: (context, sizeLevel, frameWidth) {
///     return Column(
///       children: [
///         // 您的內容
///       ],
///     );
///   },
/// )
/// ```
///
/// 參數說明：
/// - backgroundColor: 背景顏色（選填）
/// - alignment: 內容對齊方式（選填）
/// - isScrollable: 是否啟用捲動（預設為 false）
/// - builder: 內容建構函數，接收 context、sizeLevel 和 frameWidth 參數
///
/// 注意事項：
/// - 此元件會自動處理 SafeArea，確保內容不會被系統 UI 遮擋
/// - 內部佈局建議使用 Flex 或 Column/Row 的 spacing 屬性來保持一致的間距
/// - 需要自定義間距時，可使用 SizedBox 進行調整
/// - 當螢幕寬度超過 L 斷點時，內容寬度會被限制在 L 斷點值
class AutoFrame extends StatelessWidget {
  final Color? backgroundColor;
  final AlignmentGeometry? alignment;
  final FrameBuilder builder;

  const AutoFrame({super.key, this.backgroundColor, this.alignment, required this.builder});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final sizeLevel = SizeLevel.values.reversed.firstWhere((e) => width >= e.breakpoint, orElse: () => SizeLevel.S);
    // 如果寬度超過 L 的斷點，則使用 L 的斷點
    final frameWidth = width >= SizeLevel.L.breakpoint ? SizeLevel.L.breakpoint : width;
    console.log('sizeLevel: $sizeLevel, frameWidth: $frameWidth');

    Widget child = Align(
      alignment: Alignment.topCenter,
      child: Container(
        width: frameWidth,
        // 限定可用區域為可視高度，這樣裡面使用 ScrollView 不會出現錯誤
        height: MediaQuery.of(context).size.height,
        color: backgroundColor,
        child: builder(context, sizeLevel, frameWidth),
      ),
    );

    return SafeArea(child: Scaffold(body: child));
  }
}
