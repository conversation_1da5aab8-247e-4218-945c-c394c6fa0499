class ChineseNumber {
  static String parse(int number, {bool upper = false}) {
    // 中文數字對應表
    const digitU = <String>['零', '壹', '貳', '參', '肆', '伍', '陸', '柒', '捌', '玖'];
    const digitL = <String>['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const unitsU = <String>['', '拾', '佰', '仟'];
    const unitsL = <String>['', '十', '百', '千'];
    const bigUnits = <String>['', '萬', '億', '兆', '京', '垓', '秭', '穰', '溝', '澗', '正', '載', '極'];
    var digits = digitL;
    var units = unitsL;

    if (number == 0) return '零';

    bool isNegative = number < 0;
    String numStr = number.abs().toString();
    List<String> result = [];

    // 按四位分組（萬、億、兆）
    for (int i = 0; i < numStr.length; i += 4) {
      int start = numStr.length - i - 4;
      start = start < 0 ? 0 : start;
      String section = numStr.substring(start, numStr.length - i);

      // 處理每個四位數段
      String sectionResult = '';
      bool hasNonZero = false;
      bool needsZero = false;

      for (int j = 0; j < section.length; j++) {
        int digit = int.parse(section[j]);
        int unitIndex = section.length - 1 - j;

        if (digit == 0) {
          needsZero = hasNonZero;
        } else {
          if (needsZero) sectionResult += '零';
          sectionResult += digits[digit] + units[unitIndex];
          hasNonZero = true;
          needsZero = false;
        }
      }

      // 去除多餘的「一十」開頭
      if (sectionResult.startsWith('一十') && section.length <= 2) {
        sectionResult = sectionResult.substring(1);
      }

      // 添加大單位（萬、億、兆）
      if (sectionResult.isNotEmpty) {
        int bigUnitIndex = i ~/ 4;
        sectionResult += bigUnits[bigUnitIndex];
        result.insert(0, sectionResult);
      }
    }

    String finalResult = result.join('');

    // 處理跨組的零（如10001應該是一萬零一，而不是一萬一）
    // 但不應該在萬後面跟著千位數時插入零（如12345不應該是一萬零二千三百四十五）
    finalResult = finalResult.replaceAllMapped(
      RegExp(r'([萬億兆])([一二三四五六七八九])(?!千|百|十)'),
      (match) => '${match.group(1)}零${match.group(2)}',
    );

    // 轉換為大寫
    if (upper) {
      digits = digitU;
      finalResult = finalResult.replaceAllMapped(RegExp(r'[一-九]'), (match) {
        return digits[digitL.indexOf(match.group(0)!)];
      });
      units = unitsU;
      finalResult = finalResult.replaceAllMapped(RegExp(r'[十百千]'), (match) {
        return units[unitsL.indexOf(match.group(0)!)];
      });
    }
    // 處理負數
    if (isNegative) finalResult = '負$finalResult';

    return finalResult;
  }
}
