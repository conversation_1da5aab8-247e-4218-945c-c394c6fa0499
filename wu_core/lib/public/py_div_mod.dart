/// a: 被除數, b: 除數
/// 模擬 Python 的 divmod 函數，回傳 (商數, 餘數)。
///
/// 當 dividend 為負數時，餘數的符號與 divisor 相同。
/// 例如：pythonDivMod(-10, 3) == (-4, 2)
/// print(-1 // 24 = -1, -3 // 24 = -1, -1 % 24 = 23, -2 % 24 = 22)
(int, int) pyDivMod(int dividend, int divisor) {
  int quotient; // 商數 (Floor division)
  int remainder; // 餘數

  if (dividend < 0) {
    quotient = 0;
    remainder = dividend;
    while (remainder < 0) {
      quotient--;
      remainder += divisor;
    }
  } else {
    quotient = dividend ~/ divisor; // 商數 (Floor division)
    remainder = dividend % divisor; // 餘數
  }

  return (quotient, remainder);
}
