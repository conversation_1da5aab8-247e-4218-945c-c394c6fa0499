import 'package:flutter_test/flutter_test.dart';
import 'package:wu_core/public/chinese_number.dart';

void main() {
  group('ChineseNumber', () {
    group('parse', () {
      test('應該正確轉換小數字 (0-10)', () {
        expect(ChineseNumber.parse(0), '零');
        expect(ChineseNumber.parse(1), '一');
        expect(ChineseNumber.parse(5), '五');
        expect(ChineseNumber.parse(9), '九');
        expect(ChineseNumber.parse(10), '十');
      });

      test('應該正確轉換兩位數字 (11-99)', () {
        expect(ChineseNumber.parse(11), '十一');
        expect(ChineseNumber.parse(20), '二十');
        expect(ChineseNumber.parse(35), '三十五');
        expect(ChineseNumber.parse(99), '九十九');
      });

      test('應該正確轉換三位數字 (100-999)', () {
        expect(ChineseNumber.parse(100), '一百');
        expect(ChineseNumber.parse(101), '一百零一');
        expect(ChineseNumber.parse(110), '一百一十');
        expect(ChineseNumber.parse(111), '一百一十一');
        expect(ChineseNumber.parse(999), '九百九十九');
      });

      test('應該正確轉換四位數字 (1000-9999)', () {
        expect(ChineseNumber.parse(1000), '一千');
        expect(ChineseNumber.parse(1001), '一千零一');
        expect(ChineseNumber.parse(1010), '一千零一十');
        expect(ChineseNumber.parse(1100), '一千一百');
        expect(ChineseNumber.parse(9999), '九千九百九十九');
      });

      test('應該正確轉換萬位數字', () {
        expect(ChineseNumber.parse(10000), '一萬');
        expect(ChineseNumber.parse(10001), '一萬零一');
        expect(ChineseNumber.parse(12345), '一萬二千三百四十五');
        expect(ChineseNumber.parse(100000), '十萬');
        expect(ChineseNumber.parse(1000000), '一百萬');
      });

      test('應該正確轉換億位數字', () {
        expect(ChineseNumber.parse(100000000), '一億');
        expect(ChineseNumber.parse(123456789), '一億二千三百四十五萬六千七百八十九');
      });

      test('應該正確處理負數', () {
        expect(ChineseNumber.parse(-1), '負一');
        expect(ChineseNumber.parse(-123), '負一百二十三');
      });

      test('應該正確使用大寫數字', () {
        expect(ChineseNumber.parse(0, upper: true), '零');
        expect(ChineseNumber.parse(1, upper: true), '壹');
        expect(ChineseNumber.parse(9, upper: true), '玖');
        expect(ChineseNumber.parse(10, upper: true), '拾');
        expect(ChineseNumber.parse(100, upper: true), '壹佰');
        expect(ChineseNumber.parse(1000, upper: true), '壹仟');
        expect(ChineseNumber.parse(10000, upper: true), '壹萬');
      });
    });
  });
}
