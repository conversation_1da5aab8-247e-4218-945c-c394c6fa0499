name: adv_yijing
description: "AI YIJING"

publish_to: 'none' 

version: 1.0.0+1

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter

  # database
  path_provider: ^2.1.5
  hive_ce: ^2.11.2
  hive_ce_flutter: ^2.3.1

  cupertino_icons: ^1.0.8
  intl: ^0.20.2
  get: ^4.7.2
  get_storage: ^2.1.1
  flutter_markdown: ^0.7.7+1
  flutter_form_builder: ^10.0.1
  form_builder_validators: ^11.1.2
  grouped_list: ^6.0.0
  # geocoding: ^3.0.0
  flex_color_scheme: ^8.2.0
  clipboard: ^0.1.3
  flutter_svg: ^2.1.0
  url_launcher: ^6.3.1
  screenshot: ^3.0.0

  shorebird_code_push: ^2.0.3

  wu_core:
    path: ../adv_package/wu_core
  wu_llm:
    path: ../adv_package/wu_llm
  wu_fortune:
    path: ../adv_package/wu_fortune

dependency_overrides:
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  # dart run build_runner build
  # flutter packages pub run build_runner build

  hive_ce_generator: ^1.9.1

  flutter_launcher_icons: ^0.14.3
  # dart run flutter_launcher_icons

  flutter_native_splash: ^2.4.6
  # dart run flutter_native_splash:create

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/adv_yijing.png"
  image_path_android: "assets/adv_yijing.png"
  remove_alpha_ios: true
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true

flutter_native_splash:
  android: true
  ios: true
  color: "#C9BC9C"
  image: assets/adv_yijing.png
  color_dark: "#1a1a1a"
  fullscreen: true

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/docs/
    - assets/cards/
    - assets/cards/八卦/
    - assets/cards/銅錢/
    - assets/cards/骰子/
    - assets/images/signs/
    - assets/images/buttons/
    - assets/images/bagua/
    - assets/images/level/
  
  
  
  

  
  

  
  

  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
