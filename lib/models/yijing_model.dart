import 'package:hive_ce/hive.dart';
import 'package:wu_fortune/fortune/yijing.dart';

import '../hive/hive_service.dart';
import 'model_clone.dart';

class PickerData {
  final String name; // 取卦方法
  final String note; // 取卦細節
  final String sign6; // 六爻
  PickerData({
    this.name = '',
    this.note = '',
    this.sign6 = '',
  });

  String get yijingName => Yijing(sign6).name;
}

enum ReadingMode {
  yijing("易經"), // 易經式解讀
  gaodao("高島"), // 高島流解讀
  tiyong("體用"), // 體用式解讀
  liuyao("六爻"), // 六爻式解讀
  ;

  final String name;
  const ReadingMode(this.name);
}

class YijingModel extends HiveObject implements ModelClone {
  DateTime createAt = DateTime.now();
  DateTime updateAt = DateTime.now();

  String gender = "男"; // 問卦者性別
  String question = ""; // 問題
  String? additional; // 問題補充
  String? divineAtSolar; // 陽曆起卦時間，案例可能沒有陽曆起卦時間
  String? divineAtGzDate; // 天干曆起卦時間
  PickerData? pickerData; // 取卦內容
  ReadingMode readingMode = ReadingMode.yijing; // 解讀模式
  String? readingYijing; // 易經式解讀
  String? readingGaodao; // 高島流解讀
  String? readingTiyong; // 體用式解讀
  String? readingLiuyao; // 六爻式解讀
  String? summary; // 解讀摘要
  String? comment; // 回饋備注
  List<String> tags = []; // 標籤

  YijingModel({
    this.gender = "男",
    this.question = "",
    this.additional,
    this.divineAtSolar,
    this.divineAtGzDate,
    this.pickerData,
    this.readingMode = ReadingMode.yijing,
    this.readingYijing,
    this.readingGaodao,
    this.readingTiyong,
    this.readingLiuyao,
    this.summary,
    this.comment,
    this.tags = const [],
  });

  @override
  Future<void> save() async {
    if (isInBox == false) {
      await HiveService.I.box.add(this);
    } else {
      updateAt = DateTime.now();
      await super.save();
    }
  }

  @override
  YijingModel clone() {
    return YijingModel()..question = question;
  }
}
