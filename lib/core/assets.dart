import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';

class Assets {
  static Widget cardBack({double? width}) {
    return Image.asset('assets/cards/牌背.png', width: width);
  }

  static Widget cardCoins(String id, {double? width}) {
    return Image.asset('assets/cards/銅錢/$id.png', width: width);
  }

  static Widget cardGua8(String id, {double? width}) {
    return Image.asset('assets/cards/八卦/$id.png', width: width);
  }

  static Widget cardDice(String id, {double? width}) {
    return Image.asset('assets/cards/骰子/$id.png', width: width);
  }

  static Widget imgSign(String id, {double? width}) {
    return SvgPicture.asset('assets/images/signs/sign$id.svg', width: width);
  }

  static Widget imgGua8(int id, {double? width}) {
    return SvgPicture.asset('assets/images/bagua/bagua$id.svg', width: width);
  }

  static Widget imgLevel(int id, {double? width}) {
    return Image.asset('assets/images/level/level$id.png', width: width);
  }
}
