import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

import 'pages/home/<USER>';
import 'hive/hive_service.dart';

final updater = ShorebirdUpdater();
Future<void> _checkForUpdates() async {
  // Check whether a patch is available to install.
  final updaterState = await updater.checkForUpdate();

  if (updaterState == UpdateStatus.outdated) {
    // Download the new patch if it's available.
    await updater.update();
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await _checkForUpdates();
  await GetStorage.init();
  await HiveService.I.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    const scheme = FlexScheme.amber;
    final appBarStyle = FlexAppBarStyle.primary;
    final tabBarStyle = FlexTabBarStyle.forAppBar;

    return GetMaterialApp(
      title: "Meiyi",
      debugShowCheckedModeBanner: false,
      theme: FlexThemeData.light(
        scheme: scheme,
        appBarStyle: appBarStyle,
        tabBarStyle: tabBarStyle,
        useMaterial3: true,
      ),
      home: const HomePage(),
    );
  }
}
