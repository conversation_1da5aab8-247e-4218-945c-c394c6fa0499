// Generated by Hive CE
// Do not modify
// Check in to version control

import 'package:hive_ce/hive.dart';
import 'package:adv_yijing/hive/hive_adapters.dart';

extension HiveRegistrar on HiveInterface {
  void registerAdapters() {
    registerAdapter(PickerDataAdapter());
    registerAdapter(ReadingModeAdapter());
    registerAdapter(YijingModelAdapter());
  }
}

extension IsolatedHiveRegistrar on IsolatedHiveInterface {
  void registerAdapters() {
    registerAdapter(PickerDataAdapter());
    registerAdapter(ReadingModeAdapter());
    registerAdapter(YijingModelAdapter());
  }
}
