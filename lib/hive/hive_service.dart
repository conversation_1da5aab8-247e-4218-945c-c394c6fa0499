import 'dart:io';

import 'package:adv_yijing/hive/hive_registrar.g.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

import '../models/yijing_model.dart';

class HiveService {
  static HiveService I = HiveService();
  HiveService();

  final boxName = "yijing";
  late Box<YijingModel> box;

  Future<void> init() async {
    final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
    Hive
      ..init(appDocumentsDir.path)
      ..registerAdapters();
    box = await Hive.openBox<YijingModel>(boxName);
  }

  Future<void> clear() async {
    await Hive.close();
    await Hive.deleteBoxFromDisk(boxName);
    await init();
  }
}
