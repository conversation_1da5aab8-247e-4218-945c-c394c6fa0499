import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_fortune/wu_widgets.dart';

class JieqiPage extends StatefulWidget {
  const JieqiPage({super.key});

  @override
  State<JieqiPage> createState() => _JieqiPageState();
}

class _JieqiPageState extends State<JieqiPage> {
  int year = DateTime.now().year;

  @override
  Widget build(BuildContext context) {
    final headerStyle = Get.theme.textTheme.headlineSmall;

    return Scaffold(
        appBar: AppBar(
          title: const Text('節氣'),
        ),
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            spacing: 8,
            children: [
              Text("$year 年節氣表", style: headerStyle),
              T<PERSON><PERSON><PERSON><PERSON>(year: year, textScale: 1.2),
            ],
          ),
        ));
  }
}
