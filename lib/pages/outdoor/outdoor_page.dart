import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_fortune/wu_widgets.dart';

class OutdoorPage extends StatefulWidget {
  const OutdoorPage({super.key});

  @override
  State<OutdoorPage> createState() => _OutdoorPageState();
}

class _OutdoorPageState extends State<OutdoorPage> {
  DateTime solar = DateTime.now();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Get.theme.colorScheme;
    final textStyle = TextStyle(fontSize: 16, color: colorScheme.onSurface);
    return CupertinoPageScaffold(
      child: GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity! < 0) {
            // Swipe left
            solar = solar.add(Duration(days: 1));
          } else if (details.primaryVelocity! > 0) {
            // Swipe right
            solar = solar.add(Duration(days: -1));
          }
          setState(() {});
        },
        child: <PERSON><PERSON>rea(
          child: SingleChildScrollView(
            child: DefaultTextStyle(
              style: textStyle,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Column(
                  spacing: 8,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                            onPressed: () {
                              solar = solar.add(Duration(days: -1));
                              setState(() {});
                            },
                            icon: Icon(Icons.arrow_back_ios)),
                        TileSolar(solar: solar),
                        IconButton(
                            onPressed: () {
                              solar = solar.add(Duration(days: 1));
                              setState(() {});
                            },
                            icon: Icon(Icons.arrow_forward_ios)),
                      ],
                    ),
                    TileOutdoor(solar: solar, rowCount: 6, textScale: 1.0),
                    ...buildDoorList(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(child: TileLunar(solar: solar)),
                        Expanded(child: TileTibetan(solar: solar)),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> buildDoorList() {
    return [
      buildDoorTile("休", "放鬆、恢復體力", "像冥想、睡眠，精神恢復"),
      buildDoorTile("生", "健康、生命力、財運", "像喝營養飲料，讓身心都強健"),
      buildDoorTile("開", "開始、貴人、事業運", "像推開一扇好運之門，遇到幫助你的人"),
      buildDoorTile("景", "表現、考試、靈感", "像聚光燈照著你，發揮出最佳狀態"),
    ];
  }

  Widget buildDoorTile(
    String doorName,
    String title,
    String subtitle,
  ) {
    final colorScheme = Get.theme.colorScheme;
    return Card(
      elevation: 4.0,
      clipBehavior: Clip.antiAlias,
      color: colorScheme.secondaryContainer,
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: colorScheme.tertiary,
            borderRadius: BorderRadius.circular(5),
          ),
          child: Text(doorName,
              style: TextStyle(
                fontSize: 24,
                height: 1.0,
                color: colorScheme.onTertiary,
              )),
        ),
        title: Text(title),
        subtitle: Text(subtitle),
      ),
    );
  }
}
