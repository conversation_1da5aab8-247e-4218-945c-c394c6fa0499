import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';

final _llmList = ["ChatGPT", "Gemini", "Groq"];

class LlmSettingTile extends StatefulWidget {
  const LlmSettingTile({super.key});

  @override
  State<LlmSettingTile> createState() => _LlmSettingTileState();
}

class _LlmSettingTileState extends State<LlmSettingTile> {
  String llmService = _llmList.first;
  LlmServiceBase llmServiceInstance = LlmChatGpt();
  List<String> llmModels = [];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        FormBuilderDropdown(
          name: "llmService",
          items: _llmList.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
          onChanged: (value) {
            llmService = value ?? _llmList.first;

            setState(() {});
          },
        ),
        FormBuilderTextField(
          name: "llmApi<PERSON>ey",
          decoration: InputDecoration(
            labelText: "API 金鑰",
            suffix: IconButton(
                onPressed: () {
                  llmModels = ["1", "2", "3"];
                  setState(() {});
                },
                icon: Icon(Icons.refresh)),
          ),
        ),
        FormBuilderDropdown(
          name: "llmModel",
          items: llmModels.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
        ),
      ],
    );
  }
}
