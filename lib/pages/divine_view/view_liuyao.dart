import 'package:adv_yijing/core/assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_core/wu_public.dart';
import 'package:wu_fortune/wu_calendar.dart';

import '../../models/yijing_model.dart';

class ViewLiuyao extends StatelessWidget {
  const ViewLiuyao({super.key});

  @override
  Widget build(BuildContext context) {
    final model = Get.find<YijingModel>();
    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    final gzDict = GzDate.toDict(model.divineAtGzDate ?? '');
    final layout = yijing.getLiuyaoLayout(GanZhi.byName(gzDict["月"]), GanZhi.byName(gzDict["日"]));

    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        spacing: 8,
        children: [
          buildLiuyaoTable(layout),
          Expanded(child: buildGodsTable(layout)),
        ],
      ),
    );
  }

  /// 六爻列表
  Widget buildLiuyaoTable(Map layout) {
    final gua64s = <Gua64>[
      layout["伏卦"] as Gua64,
      layout["本卦"] as Gua64,
      layout["變卦"] as Gua64,
    ];
    final colorScheme = Get.theme.colorScheme;
    final textScaler = TextScaler.linear(1.5);

    Widget headerText(String text) {
      return Text(
        text,
        textAlign: TextAlign.center,
        style: TextStyle(color: colorScheme.onSecondaryContainer),
      );
    }

    Widget normalText(String text, {TextScaler? textScaler}) {
      return Text(
        text,
        textAlign: TextAlign.center,
        textScaler: textScaler,
      );
    }

    return Table(
      columnWidths: {
        0: FlexColumnWidth(1),
        1: FlexColumnWidth(2),
        2: FlexColumnWidth(1),
        3: FlexColumnWidth(2),
        4: FlexColumnWidth(1),
        5: FlexColumnWidth(2),
      },
      border: TableBorder.all(color: Colors.grey),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      children: [
        TableRow(children: [
          SizedBox(),
          normalText("${gua64s[0].fullname}\n${gua64s[0].dai}"),
          SizedBox(),
          normalText("${gua64s[1].fullname}\n${gua64s[1].dai}"),
          SizedBox(),
          normalText("${gua64s[2].fullname}\n${gua64s[2].dai}"),
        ]),
        TableRow(
            decoration: BoxDecoration(
              color: colorScheme.secondaryContainer,
            ),
            children: [
              SizedBox(),
              headerText("伏卦"),
              SizedBox(),
              headerText("本卦"),
              SizedBox(),
              headerText("變卦"),
            ]),
        ...List.generate(6, (index) {
          final yao = layout["爻"][index] as Map<String, dynamic>;
          final shiyaoIndex = layout["世爻序"] as int;
          var shiying = '';
          if ((5 - index) == shiyaoIndex) shiying = "世";
          if ((5 - index) == (shiyaoIndex + 3) % 6) shiying = "應";
          return TableRow(children: [
            Text(yao["六獸"], textAlign: TextAlign.center),
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: normalText(
                '${yao["伏爻支"]}${yao["伏爻親"]}',
                textScaler: textScaler,
              ),
            ),
            Text(shiying, textAlign: TextAlign.center),
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: normalText(
                '${yao["本爻支"]}${yao["本爻親"]}',
                textScaler: textScaler,
              ),
            ),
            Assets.imgSign(yao["符號"], width: 20),
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: normalText(
                '${yao["變爻支"]}${yao["變爻親"]}',
                textScaler: textScaler,
              ),
            ),
          ]);
        })
      ],
    );
  }

  /// 神煞列表
  Widget buildGodsTable(Map layout) {
    final gods = layout["神煞"] as Map<String, String>;
    return TableBuilder(
      border: TableBorder.all(color: Colors.grey),
      itemCount: gods.length,
      columnCount: 4,
      cellBuilder: (index) {
        final e = gods.entries.elementAt(index);
        return CapsuleText(leftText: e.key, rightText: e.value);
      },
    );
  }
}
