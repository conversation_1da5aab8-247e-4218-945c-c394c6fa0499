import 'package:adv_yijing/pages/divine_view/view_liuyao.dart';
import 'package:adv_yijing/pages/divine_view/view_tiyong.dart';
import 'package:adv_yijing/pages/divine_view/view_yijing.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/yijing_model.dart' as yijing_model;
import 'date_row.dart';
import 'reading_page.dart';

final _viewList = {
  "觀卦": {
    "易經": const ViewYijing(),
    "六爻": const ViewLiuyao(),
    "體用": const ViewTiyong(),
  },
  "解讀": {
    "高島": ReadingPage(mode: "高島"),
    "體用": ReadingPage(mode: "體用"),
    "易經": ReadingPage(mode: "易經"),
    "六爻": ReadingPage(mode: "六爻"),
  }
};

class DivineViewPage extends StatefulWidget {
  final yijing_model.YijingModel model;
  const DivineViewPage({super.key, required this.model});

  @override
  State<DivineViewPage> createState() => _DivineViewPageState();
}

class _DivineViewPageState extends State<DivineViewPage> {
  String level1 = '';
  String level2 = '';

  @override
  void initState() {
    level1 = _viewList.keys.first;
    level2 = _viewList[level1]!.keys.first;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Get.put(widget.model);
    return Scaffold(
      appBar: AppBar(
        title: const Text('觀卦'),
      ),
      body: SafeArea(
        child: Column(
          spacing: 8,
          children: [
            buildHeader(),
            buildSegment(),
            Expanded(child: _viewList[level1]![level2]!),
          ],
        ),
      ),
    );
  }

  Widget buildSegment() {
    return LayoutBuilder(builder: (_, constraints) {
      final width = constraints.maxWidth * 0.9;
      return Column(
        spacing: 8,
        children: [
          SizedBox(
            width: width,
            child: CupertinoSegmentedControl<String>(
                groupValue: level1,
                children: {for (var e in _viewList.keys) e: Text(e)},
                onValueChanged: (value) {
                  level1 = value;
                  level2 = _viewList[level1]!.keys.first;
                  setState(() {});
                }),
          ),
          SizedBox(
            width: width,
            child: CupertinoSegmentedControl<String>(
                groupValue: level2,
                children: {for (var e in _viewList[level1]!.keys) e: Text(e)},
                onValueChanged: (value) {
                  level2 = value;
                  setState(() {});
                }),
          ),
        ],
      );
    });
  }

  Widget buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Column(
        spacing: 8,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DateRow(model: widget.model),
          Text(widget.model.question, style: Get.textTheme.headlineSmall),
        ],
      ),
    );
  }
}
