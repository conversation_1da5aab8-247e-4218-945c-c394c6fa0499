import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';
import 'package:wu_fortune/wu_fortune.dart';

import '../../models/yijing_model.dart';
import '../../services/llm_prompts.dart';

class ReadingPage extends StatelessWidget {
  final String mode;
  const ReadingPage({super.key, required this.mode});

  @override
  Widget build(BuildContext context) {
    final model = Get.find<YijingModel>();
    final LlmPrompts llmPrompts = LlmPrompts(model);
    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    if ((yijing.dongCount != 1) && (["高島", "體用"].contains(mode))) {
      return Center(
        child: Text(
          "這個卦有${yijing.dongCount}個動爻\n$mode只適合一爻動的卦象",
          textAlign: TextAlign.center,
        ),
      );
    }
    String sysPrompt, userPrompt;
    switch (mode) {
      case "易經":
        (sysPrompt, userPrompt) = llmPrompts.getYijingPrompt();
        break;
      case "高島":
        (sysPrompt, userPrompt) = llmPrompts.getGaodaoPrompt();
        break;
      case "體用":
        (sysPrompt, userPrompt) = llmPrompts.getTiyongPrompt();
        break;
      case "六爻":
        (sysPrompt, userPrompt) = llmPrompts.getLiuyaoPrompt();
        break;
      default:
        (sysPrompt, userPrompt) = llmPrompts.getYijingPrompt();
        break;
    }
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          OverflowBar(
            alignment: MainAxisAlignment.end,
            spacing: 8,
            children: [
              IconButton(
                onPressed: () {
                  FlutterClipboard.copy('$sysPrompt\n\n$userPrompt').then((value) {
                    Get.snackbar("複製成功", "複製成功");
                  });
                },
                icon: Icon(Icons.copy_all),
              ),
            ],
          ),
          MarkdownBody(data: sysPrompt),
          MarkdownBody(data: userPrompt),
        ],
      ),
    );
  }
}
