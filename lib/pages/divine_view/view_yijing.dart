import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_fortune/wu_fortune.dart';

import '../../core/assets.dart';
import '../../models/yijing_model.dart';

class ViewYijing extends StatelessWidget {
  const ViewYijing({super.key});

  @override
  Widget build(BuildContext context) {
    final model = Get.find<YijingModel>();
    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    final gua64Dict = {
      "本卦": Gua64.bySign6(yijing.orginal),
      "互卦": Gua64.bySign6(yijing.interaction),
      "變卦": Gua64.bySign6(yijing.changed),
      "錯卦": Gua64.bySign6(yijing.swap),
      "綜卦": Gua64.bySign6(yijing.reverse),
    };
    return Scaffold(
      body: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: gua64Dict.length,
          itemBuilder: (_, index) {
            final e = gua64Dict.entries.elementAt(index);
            return Column(children: [
              Text(e.key),
              Text(e.value.fullname),
              Assets.imgGua8(e.value.gua8s[0].index, width: 100),
              Assets.imgGua8(e.value.gua8s[1].index, width: 100),
            ]);
          }),
    );
  }
}
