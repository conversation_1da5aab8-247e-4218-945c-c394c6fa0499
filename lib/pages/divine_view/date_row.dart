import 'package:flutter/material.dart';
import 'package:wu_core/wu_extensions.dart';
import 'package:wu_fortune/wu_calendar.dart';

import '../../models/yijing_model.dart';

class DateRow extends StatelessWidget {
  final YijingModel model;
  const DateRow({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildGzdate(model),
        Expanded(child: _buildDates(model)),
      ],
    );
  }

  Widget _buildDates(YijingModel model) {
    final divinRows = <Widget>[];
    if (model.divineAtSolar?.isNotEmpty != false) {
      final divineAt = DateTime.parse(model.divineAtSolar!);
      final jieqi1 = Jieqi.bySolar(divineAt);
      final jieqi2 = Jieqi.byYearIndex(divineAt.year, jieqi1.index + 1);
      divinRows.add(Text("起卦：${divineAt.ymdhm()}"));
      divinRows.add(Text("${jieqi1.name} ${jieqi1.dateTime.md()} ~ ${jieqi2.name} ${jieqi2.dateTime.md()}"));
    }
    final gzDict = GzDate.toDict(model.divineAtGzDate ?? '');
    final ganzhi = GanZhi.byName(gzDict["日"]);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("建檔：${model.createAt.ymdhm()}"),
        ...divinRows,
        Text("旬空：${ganzhi.xunkong}"),
      ],
    );
  }

  Widget _buildGzdate(YijingModel model) {
    final dict = GzDate.toDict(model.divineAtGzDate ?? '');
    final keys = dict.keys.toList();

    Widget headerText(String text) {
      return Text(text, textAlign: TextAlign.center);
    }

    Widget cellText(String key, String text) {
      var fontColor = Colors.black;
      if (["月", "日"].contains(key) && Zhi.names.contains(text)) {
        fontColor = Zhi.byName(text).color;
      }
      return SizedBox(
        height: 40,
        child: Center(
          child: Text(
            text,
            style: TextStyle(fontSize: 30, height: 1.0, color: fontColor),
          ),
        ),
      );
    }

    return SizedBox(
        width: 200,
        child: Table(
          border: TableBorder.all(color: Colors.grey),
          children: [
            TableRow(children: keys.map<Widget>((e) => headerText(e)).toList()),
            TableRow(children: keys.map<Widget>((e) => cellText(e, dict[e]![0])).toList()),
            TableRow(children: keys.map<Widget>((e) => cellText(e, dict[e]![1])).toList()),
          ],
        ));
  }
}
