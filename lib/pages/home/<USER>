import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wu_core/wu_public.dart';

import '../divine_list/divine_list_page.dart';
import '../jieqi/jieqi_page.dart';
import '../outdoor/outdoor_page.dart';

final _menus = <MenuItem>[
  MenuItem(title: '出行', normalIcon: Icon(Icons.follow_the_signs_outlined), body: OutdoorPage()),
  MenuItem(title: '占卜', normalIcon: Icon(Icons.question_answer_outlined), body: DivineListPage()),
  MenuItem(title: '節氣', normalIcon: Icon(Icons.sunny_snowing), body: JieqiPage()),
  MenuItem(title: '文獻', normalIcon: Icon(Icons.library_books_outlined)),
  MenuItem(title: '設定', normalIcon: Icon(Icons.settings_outlined)),
];

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: CupertinoTabScaffold(
          tabBar: CupertinoTabBar(
              currentIndex: _selectedIndex,
              onTap: (index) {
                setState(() => _selectedIndex = index);
              },
              items: _menus
                  .map((e) => BottomNavigationBarItem(
                        icon: e.normalIcon,
                        label: e.title,
                      ))
                  .toList()),
          tabBuilder: (_, index) {
            return _menus[index].body;
          }),
    );
  }
}
