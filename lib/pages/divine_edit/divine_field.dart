import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/yijing_model.dart';
import 'divine_field_picker.dart';

class DivineField extends FormField<PickerData> {
  DivineField({
    super.key,
    String invalidText = '請按圖示取卦',
    Function(PickerData? data)? super.onSaved,
    InputDecoration? decoration = const InputDecoration(),
  }) : super(
          validator: (data) {
            final result = ((data?.sign6 ?? '').length == 6) ? null : invalidText;
            return result;
          },
          builder: (field) {
            return DivineFieldWidget(
              decoration: decoration,
              errorText: field.errorText,
              onChange: ((newData) => field.didChange(newData)),
              onSaved: onSaved,
              value: field.value,
            );
          },
        );
}

class DivineFieldWidget extends StatefulWidget {
  final InputDecoration? decoration;
  final void Function(PickerData data)? onChange;
  final void Function(PickerData data)? onSaved;
  final String? errorText;
  final PickerData? value;

  const DivineFieldWidget({
    super.key,
    this.decoration = const InputDecoration(),
    this.onChange,
    this.onSaved,
    this.errorText,
    this.value,
  });

  @override
  State<DivineFieldWidget> createState() => _DivineFieldWidgetState();
}

class _DivineFieldWidgetState extends State<DivineFieldWidget> {
  PickerData? data;

  @override
  void initState() {
    super.initState();
    data = widget.value;
  }

  @override
  void didUpdateWidget(DivineFieldWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      data = widget.value;
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return InputDecorator(
        decoration: widget.decoration!.copyWith(
          errorText: widget.errorText,
          suffix: IconButton(
            icon: Icon(
              Icons.swipe_up,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onPressed: () async {
              var newData = await Get.to(const DivineFieldPicker());
              if (newData == null) return;
              widget.onChange?.call(newData);
              data = newData;
              setState(() {});
            },
          ),
        ),
        child: data == null
            ? SizedBox(height: 30)
            : Row(
                spacing: 4,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    height: 30,
                    width: 80,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: colorScheme.primary,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      data!.name,
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.onPrimary,
                      ),
                    ),
                  ),
                  Text(data!.note, style: textTheme.titleMedium),
                ],
              ));
  }
}
