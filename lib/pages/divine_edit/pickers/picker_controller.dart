import 'package:get/get.dart';

import '../../../models/yijing_model.dart';

export 'package:get/get.dart';
export '../../../models/yijing_model.dart';
export 'package:wu_fortune/wu_fortune.dart';

class PickerController extends GetxController {
  PickerData _pickerData = PickerData();

  PickerData get pickerData => _pickerData;

  set pickerData(PickerData value) {
    _pickerData = value;
    update();
  }

  bool get isRolled => pickerData.sign6.length == 6;
}
