import 'package:flutter/material.dart';
import 'package:wu_core/wu_public.dart';

import '../../../core/assets.dart';
import 'picker_controller.dart';
import 'result_tile.dart';

const _diceNames = ["一", "二", "三", "四", "五", "六"];

final _cardGroup = [
  Gua8.names,
  Gua8.names,
  _diceNames,
];

/// 卡牌3數起卦
class CardNum3 extends StatefulWidget {
  const CardNum3({super.key});

  @override
  State<CardNum3> createState() => _CardNum3State();
}

class _CardNum3State extends State<CardNum3> {
  final List<String> result = [];

  void updatePickerData() {
    final controller = Get.find<PickerController>();
    final yijing = Yijing.byNum3(
      Gua8.byName(result[0]).index,
      Gua8.byName(result[1]).index,
      _diceNames.indexOf(result[2]),
    );
    controller.pickerData = PickerData(
      name: '三卡牌',
      note: '${result.join()} 得卦 ${yijing.name}',
      sign6: yijing.sign6,
    );
  }

  @override
  Widget build(BuildContext context) {
    var cards = result.length < _cardGroup.length ? List.from(_cardGroup[result.length]) : [];
    return Container(
      color: Theme.of(context).colorScheme.secondaryContainer,
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
      child: GetBuilder<PickerController>(builder: (controller) {
        cards.shuffle();
        return Column(
          spacing: 8,
          children: [
            ResultTile(
                height: 56,
                child: Row(
                  spacing: 2,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: result.map((e) => Text(e, style: TextStyle(fontSize: 26))).toList(),
                )),
            if (controller.isRolled == false)
              CardDrewer(
                redraw: true,
                flipSpeed: 400,
                cardCount: cards.length,
                frontBuilder: (index, cardWidth) {
                  return result.length == 2
                      ? Assets.cardDice(cards[index], width: cardWidth)
                      : Assets.cardGua8(cards[index], width: cardWidth);
                },
                backBuilder: (index, cardWidth) {
                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      Assets.cardBack(width: cardWidth),
                      Text(
                        (index + 1).toString(),
                        style: TextStyle(
                          fontFamily: "Arial",
                          fontSize: 32,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  );
                },
                cardSide: (index) => CardSide.BACK,
                onFlipDone: (index) {
                  result.add(cards[index]);
                  setState(() {});
                  if (result.length == _cardGroup.length) {
                    updatePickerData();
                  }
                },
              ),
          ],
        );
      }),
    );
  }
}
