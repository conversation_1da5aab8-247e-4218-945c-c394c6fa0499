import 'package:adv_yijing/pages/divine_edit/pickers/picker_controller.dart';
import 'package:adv_yijing/pages/divine_edit/pickers/result_tile.dart';
import 'package:flutter/material.dart';
import 'package:wu_core/widgets/card_drewer.dart';

import '../../../core/assets.dart';

final _cardList = {
  '人人人': "o",
  '人人文': '-',
  '人文人': '-',
  '人文文': '=',
  '文人人': '-',
  '文人文': '=',
  '文文人': '=',
  '文文文': 'x',
};

/// 卡牌金錢起卦
class CardCoins extends StatefulWidget {
  const CardCoins({super.key});

  @override
  State<CardCoins> createState() => _CardCoinsState();
}

class _CardCoinsState extends State<CardCoins> {
  String result = '';

  void addResult(String newSign) {
    if (result.length == 6) return;
    result = newSign + result;
    setState(() {});
    if (result.length == 6) {
      final controller = Get.find<PickerController>();
      final yijing = Yijing(result);
      controller.pickerData = PickerData(
        name: '銅錢牌',
        note: '$result 得卦 ${yijing.name}',
        sign6: result,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final cards = _cardList.keys.toList();
    return Container(
      color: Theme.of(context).colorScheme.secondaryContainer,
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
      child: GetBuilder<PickerController>(builder: (controller) {
        cards.shuffle();
        final signList = result.split('');
        return Column(
          spacing: 8,
          children: [
            ResultTile(
                height: 56,
                child: Row(
                  spacing: 2,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: signList.map((e) => Assets.imgSign(e)).toList(),
                )),
            if (controller.isRolled == false)
              CardDrewer(
                redraw: true,
                flipSpeed: 400,
                cardCount: cards.length,
                frontBuilder: (index, cardWidth) {
                  return Assets.cardCoins(cards[index], width: cardWidth);
                },
                backBuilder: (index, cardWidth) {
                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      Assets.cardBack(width: cardWidth),
                      Text(
                        (index + 1).toString(),
                        style: TextStyle(
                          fontFamily: "Arial",
                          fontSize: 32,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  );
                },
                cardSide: (index) => CardSide.BACK,
                onFlipDone: (index) {
                  addResult(_cardList[cards[index]] ?? '');
                },
              ),
          ],
        );
      }),
    );
  }
}
