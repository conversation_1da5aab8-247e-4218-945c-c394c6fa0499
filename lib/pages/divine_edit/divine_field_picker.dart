import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'pickers/card_coins.dart';
import 'pickers/card_num3.dart';
import 'pickers/card_num4.dart';
import 'pickers/liuyao.dart';
import 'pickers/picker_controller.dart';
import 'pickers/text_num3.dart';
import 'pickers/wheel_num3.dart';
import 'pickers/wheel_num4.dart';

final _viewList = {
  "手動": {
    "三數": const TextNum3(),
    "銅錢手": const <PERSON><PERSON><PERSON>(),
    "三滾輪": const WheelNum3(),
    "四滾輪": const WheelNum4(),
  },
  "卡牌": {
    "銅錢卡": const CardCoins(),
    "三卡牌": const CardNum3(),
    "四卡牌": const CardNum4(),
  },
};

class DivineFieldPicker extends StatefulWidget {
  const DivineFieldPicker({super.key});

  @override
  State<DivineFieldPicker> createState() => _DivineFieldPickerState();
}

class _DivineFieldPickerState extends State<DivineFieldPicker> {
  String level1 = '';
  String level2 = '';

  @override
  void initState() {
    level1 = _viewList.keys.first;
    level2 = _viewList[level1]!.keys.first;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    print("level1: $level1, level2: $level2");
    return Scaffold(
      appBar: AppBar(
        title: const Text('起卦'),
        actions: [
          GetBuilder<PickerController>(
            init: PickerController(),
            builder: (controller) {
              return TextButton(
                style: TextButton.styleFrom(
                  disabledForegroundColor: Colors.grey,
                  foregroundColor: colorScheme.onPrimary,
                ),
                onPressed: controller.isRolled ? () => Get.back(result: controller.pickerData) : null,
                child: Text("確定"),
              );
            },
          )
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: buildSegment(),
          ),
          Expanded(child: _viewList[level1]?[level2] ?? Container()),
        ],
      ),
    );
  }

  Widget buildSegment() {
    return LayoutBuilder(builder: (_, constraints) {
      final width = constraints.maxWidth * 0.9;
      return Column(
        spacing: 8,
        children: [
          SizedBox(
            width: width,
            child: CupertinoSegmentedControl<String>(
                groupValue: level1,
                children: {for (var e in _viewList.keys) e: Text(e)},
                onValueChanged: (value) {
                  level1 = value;
                  level2 = _viewList[level1]!.keys.first;
                  setState(() {});
                }),
          ),
          SizedBox(
            width: width,
            child: CupertinoSegmentedControl<String>(
                groupValue: level2,
                children: {for (var e in _viewList[level1]!.keys) e: Text(e)},
                onValueChanged: (value) {
                  level2 = value;
                  setState(() {});
                }),
          ),
        ],
      );
    });
  }
}
