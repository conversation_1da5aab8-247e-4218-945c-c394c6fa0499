import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_core/wu_public.dart';
import 'package:wu_fortune/wu_fortune.dart';

class GzdateField extends FormField<String> {
  GzdateField({
    super.key,
    super.onSaved,
    super.initialValue,
    String invalidText = '請輸入干支曆日期',
    TextEditingController? controller,
    super.validator,
    ValueChanged<String>? onChanged,
    InputDecoration? decoration = const InputDecoration(),
  }) : super(builder: (field) {
          controller ??= TextEditingController(text: initialValue);
          controller!.addListener(() {
            field.didChange(controller?.text);
          });
          decoration = decoration?.copyWith(
            suffixIcon: IconButton(
              icon: Icon(Icons.swipe_up),
              onPressed: () async {
                final result = await Get.dialog(GzdatePicker());
                if (result == null) return;
                field.didChange(result);
                onChanged?.call(result);
              },
            ),
          );
          return TextFormField(
            readOnly: true,
            controller: controller,
            decoration: decoration,
            validator: validator,
            onChanged: onChanged, // 將外部的 onChanged 傳入
          );
        });
}

class GzdatePicker extends StatefulWidget {
  final InputDecoration? decoration;
  final void Function(String data)? onChange;
  const GzdatePicker({super.key, this.decoration, this.onChange});

  @override
  State<GzdatePicker> createState() => _GzdatePickerState();
}

class _GzdatePickerState extends State<GzdatePicker> {
  List<String> result = ['-', '-', '甲', '子', '甲', '子', '-', '-'];

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: Text("取消"),
                ),
                TextButton(
                  onPressed: () {
                    var outtext = "";
                    if (!(result[0] == '-' || result[1] == '-')) outtext += "${result[0]}${result[1]}年";
                    outtext += "${result[2]}${result[3]}月";
                    outtext += "${result[4]}${result[5]}日";
                    if (!(result[6] == '-' || result[7] == '-')) outtext += "${result[6]}${result[7]}時";
                    Get.back(result: outtext);
                  },
                  child: Text("確定"),
                ),
              ],
            ),
            Table(
              children: [
                TableRow(children: [
                  Text("年", textAlign: TextAlign.center),
                  Text("月", textAlign: TextAlign.center),
                  Text("日", textAlign: TextAlign.center),
                  Text("時", textAlign: TextAlign.center),
                ]),
                TableRow(children: [
                  buildGanZhiWheel(true, 0),
                  buildGanZhiWheel(false, 2),
                  buildGanZhiWheel(false, 4),
                  buildGanZhiWheel(true, 6),
                ]),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildGanZhiWheel(bool nullable, int startIndex) {
    final ganNames = nullable ? ['-', ...Gan.names] : Gan.names;
    final zhiNames = nullable ? ['-', ...Zhi.names] : Zhi.names;
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        WheelPicker(
          items: ganNames.map((e) => WheelItem(Text(e), e)).toList(),
          width: 30,
          onSelectedItemChanged: (value) => result[startIndex] = value,
        ),
        WheelPicker(
          items: zhiNames.map((e) => WheelItem(Text(e), e)).toList(),
          width: 30,
          onSelectedItemChanged: (value) => result[startIndex + 1] = value,
        ),
      ],
    );
  }
}
