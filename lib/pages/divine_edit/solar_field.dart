import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_core/wu_extensions.dart';
import 'package:wu_fortune/wu_zeri.dart';

final _dateFormat = DateFormat("yyyy-MM-dd HH:mm");

class SolarField extends FormField<String> {
  SolarField({
    super.key,
    super.onSaved,
    super.initialValue,
    String invalidText = '請輸入陽曆日期',
    TextEditingController? controller,
    super.validator,
    ValueChanged<String>? onChanged,
    InputDecoration? decoration = const InputDecoration(),
  }) : super(builder: (field) {
          controller ??= TextEditingController(text: field.value ?? initialValue);
          decoration = decoration?.copyWith(
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () {
                    field.didChange(null);
                    onChanged?.call('');
                  },
                ),
                IconButton(
                  icon: Icon(Icons.swipe_up),
                  onPressed: () async {
                    final solar = field.value == null ? DateTime.now() : DateTime.parse(field.value ?? '');
                    final result = await Get.dialog(DialogSolarPicker(solar: solar));
                    if (result == null) return;
                    final dateStr = _dateFormat.format(result);
                    controller!.text = dateStr;
                    field.didChange(dateStr);
                    onChanged?.call(dateStr);
                  },
                ),
              ],
            ),
          );

          return TextFormField(
            readOnly: true,
            controller: controller,
            decoration: decoration,
            validator: validator,
            onChanged: onChanged,
          );
        });
}
