import 'dart:developer';

import 'package:flutter_test/flutter_test.dart';
import 'package:wu_llm/core/llm_deepseek.dart';
import 'package:wu_llm/core/llm_message.dart';

void main() {
  late LlmDeepseek llmDeepseek;
  const String testApiKey = '***********************************'; // 請替換為有效的 Deepseek API Key
  const String testModelName = 'deepseek-chat';

  setUp(() {
    llmDeepseek = LlmDeepseek();
    llmDeepseek.initDio(testApiKey);
  });

  group('LlmDeepseek 初始化測試', () {
    test('初始化應設置正確的服務名稱和預設模型', () {
      expect(llmDeepseek.serviceName, equals('Deepseek'));
      expect(llmDeepseek.defaultModelName, equals('deepseek-chat'));
      expect(llmDeepseek.apiKeyUrl, equals('https://platform.deepseek.com/usage'));
    });
  });

  group('getModels 測試', () {
    test('獲取模型列表', () async {
      final models = await llmDeepseek.getModels(testApiKey);

      // 驗證返回的模型列表不為空
      expect(models, isNotEmpty);

      // 驗證返回的模型列表包含預設模型
      expect(models, contains('deepseek-chat'));

      // 打印獲取到的模型列表以供參考
      log('獲取到的模型列表: $models');
    });
  });

  group('roleCast 測試', () {
    test('角色轉換應正確映射', () {
      expect(llmDeepseek.roleCast(LlmRole.system), equals('system'));
      expect(llmDeepseek.roleCast(LlmRole.assistant), equals('assistant'));
      expect(llmDeepseek.roleCast(LlmRole.user), equals('user'));
    });
  });

  group('chat 測試', () {
    test('進行聊天', () async {
      // 創建測試訊息
      final messages = [const LlmMessage.system('你是一個有用的助手'), const LlmMessage.user('你好，請簡短回答')];

      // 啟動聊天流
      final chatStream = llmDeepseek.chat(apiKey: testApiKey, modelName: testModelName, messages: messages);

      // 收集響應
      final responseBuffer = StringBuffer();
      await for (final chunk in chatStream) {
        if (chunk != null) {
          responseBuffer.write(chunk);
          log('收到回應片段: $chunk');
        }
      }

      final fullResponse = responseBuffer.toString();
      log('完整回應: $fullResponse');

      // 驗證響應不為空
      expect(fullResponse, isNotEmpty);
    }, timeout: const Timeout(Duration(minutes: 2)));
  });

  group('錯誤處理測試', () {
    test('使用無效的 API Key', () async {
      // 使用明顯無效的 API Key
      const invalidApiKey = 'invalid_api_key';
      llmDeepseek.initDio(invalidApiKey);

      // 創建測試訊息
      final messages = [const LlmMessage.user('你好')];

      // 啟動聊天流
      final chatStream = llmDeepseek.chat(apiKey: invalidApiKey, modelName: testModelName, messages: messages);

      // 收集錯誤響應
      final responseList = await chatStream.toList();

      // 驗證有錯誤響應
      expect(responseList, isNotEmpty);
      log('錯誤響應: ${responseList.join()}');

      // 錯誤響應應該包含錯誤信息
      expect(responseList.join().contains('錯誤') || responseList.join().contains('error'), isTrue);
    });
  });
}
