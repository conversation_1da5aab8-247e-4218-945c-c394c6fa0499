import 'dart:developer';

import 'package:flutter_test/flutter_test.dart';
import 'package:wu_llm/core/llm_claude.dart';
import 'package:wu_llm/core/llm_message.dart';

void main() {
  late LlmClaude llmClaude;
  const String testApiKey =
      '************************************************************************************************************'; // 請替換為有效的 Anthropic API Key
  const String testModelName = 'claude-3-5-haiku-********'; // 使用較小的模型以節省成本

  setUp(() {
    llmClaude = LlmClaude();
    llmClaude.initDio(testApiKey);
  });

  group('LlmClaude 初始化測試', () {
    test('初始化應設置正確的服務名稱和預設模型', () {
      expect(llmClaude.serviceName, equals('Claude'));
      expect(llmClaude.defaultModelName, equals('claude-3-5-haiku-********'));
      expect(llmClaude.apiKeyUrl, equals('https://console.anthropic.com/account/keys'));
    });
  });

  group('getModels 測試', () {
    test('獲取模型列表', () async {
      final models = await llmClaude.getModels(testApiKey);

      // 驗證返回的模型列表不為空
      expect(models, isNotEmpty);

      // 打印獲取到的模型列表以供參考
      log('獲取到的模型列表: $models');
    });
  });

  group('roleCast 測試', () {
    test('角色轉換應正確映射', () {
      expect(llmClaude.roleCast(LlmRole.system), equals('user'));
      expect(llmClaude.roleCast(LlmRole.assistant), equals('assistant'));
      expect(llmClaude.roleCast(LlmRole.user), equals('user'));
    });
  });

  group('formatMessages 測試', () {
    test('訊息格式化應正確', () {
      final messages = [const LlmMessage.system('你是一個有用的助手'), const LlmMessage.user('你好，請簡短回答')];

      final formattedMessages = llmClaude.formatMessages(messages);

      expect(formattedMessages.length, equals(2));
      expect(formattedMessages[0]['role'], equals('user'));
      expect(formattedMessages[0]['content'], equals('你是一個有用的助手'));
      expect(formattedMessages[1]['role'], equals('user'));
      expect(formattedMessages[1]['content'], equals('你好，請簡短回答'));
    });
  });

  group('chat 測試', () {
    test('進行聊天', () async {
      // 創建測試訊息
      final messages = [const LlmMessage.system('你是一個有用的助手'), const LlmMessage.user('你好，請用一句話回答')];

      // 啟動聊天流
      final chatStream = llmClaude.chat(apiKey: testApiKey, modelName: testModelName, messages: messages);

      // 收集響應
      final responseBuffer = StringBuffer();
      await for (final chunk in chatStream) {
        if (chunk != null) {
          responseBuffer.write(chunk);
          log('收到回應片段: $chunk');
        }
      }

      final fullResponse = responseBuffer.toString();
      log('完整回應: $fullResponse');

      // 驗證響應不為空
      expect(fullResponse, isNotEmpty);
    }, timeout: const Timeout(Duration(minutes: 2)));
  });

  group('錯誤處理測試', () {
    test('使用無效的 API Key', () async {
      // 使用明顯無效的 API Key
      const invalidApiKey = 'invalid_api_key';
      llmClaude.initDio(invalidApiKey);

      // 創建測試訊息
      final messages = [const LlmMessage.user('你好')];

      // 啟動聊天流
      final chatStream = llmClaude.chat(apiKey: invalidApiKey, modelName: testModelName, messages: messages);

      // 收集錯誤響應
      final responseList = await chatStream.toList();

      // 驗證有錯誤響應
      expect(responseList, isNotEmpty);
      log('錯誤響應: ${responseList.join()}');

      // 錯誤響應應該包含錯誤信息
      expect(responseList.join().contains('錯誤') || responseList.join().contains('error'), isTrue);
    });
  });
}
