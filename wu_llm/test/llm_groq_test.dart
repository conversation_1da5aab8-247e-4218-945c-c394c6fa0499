import 'dart:developer';

import 'package:flutter_test/flutter_test.dart';
import 'package:wu_llm/core/llm_groq.dart';
import 'package:wu_llm/core/llm_message.dart';

void main() {
  late LlmGroq llmGroq;
  const String testApiKey = '********************************************************';
  const String testModelName = 'llama3-8b-8192';

  setUp(() {
    llmGroq = LlmGroq();
    llmGroq.initDio(testApiKey);
  });

  group('LlmGroq 初始化測試', () {
    test('初始化應設置正確的服務名稱和預設模型', () {
      expect(llmGroq.serviceName, equals('Groq'));
      expect(llmGroq.defaultModelName, equals('llama3-8b-8192'));
      expect(llmGroq.apiKeyUrl, equals('https://console.groq.com/keys'));
    });
  });

  group('getModels 測試', () {
    test('獲取模型列表', () async {
      final models = await llmGroq.getModels(testApiKey);

      // 驗證返回的模型列表不為空
      expect(models, isNotEmpty);

      // 打印獲取到的模型列表以供參考
      log('獲取到的模型列表: $models');
    });
  });

  group('chat 測試', () {
    test('進行聊天', () async {
      // 創建測試訊息
      final messages = [const LlmMessage.system('你是一個有用的助手'), const LlmMessage.user('你好，請簡短回答')];

      // 啟動聊天流
      final chatStream = llmGroq.chat(apiKey: testApiKey, modelName: testModelName, messages: messages);

      // 收集響應
      final responseBuffer = StringBuffer();
      await for (final chunk in chatStream) {
        if (chunk != null) {
          responseBuffer.write(chunk);
          log('收到回應片段: $chunk');
        }
      }

      final fullResponse = responseBuffer.toString();
      log('完整回應: $fullResponse');

      // 驗證響應不為空
      expect(fullResponse, isNotEmpty);
    }, timeout: const Timeout(Duration(minutes: 2)));
  });

  group('錯誤處理測試', () {
    test('使用無效的 API Key', () async {
      // 使用明顯無效的 API Key
      const invalidApiKey = 'invalid_api_key';
      llmGroq.initDio(invalidApiKey);

      // 創建測試訊息
      final messages = [const LlmMessage.user('你好')];

      // 啟動聊天流
      final chatStream = llmGroq.chat(apiKey: invalidApiKey, modelName: testModelName, messages: messages);

      // 收集錯誤響應
      final responseList = await chatStream.toList();

      // 驗證有錯誤響應
      expect(responseList, isNotEmpty);
      log('錯誤響應: ${responseList.join()}');

      // 錯誤響應應該包含錯誤信息
      expect(responseList.join().contains('錯誤') || responseList.join().contains('error'), isTrue);
    });
  });
}
