import 'package:flutter_test/flutter_test.dart';
import 'package:wu_llm/core/llm_message.dart';
import 'package:wu_llm/core/llm_openrouter.dart';

void main() {
  // 測試常量
  const String testApiKey = 'YOUR_API_KEY'; // 請替換為有效的 API Key 進行測試

  group('LlmOpenRouter 測試', () {
    late LlmOpenRouter openRouter;

    setUp(() {
      openRouter = LlmOpenRouter();
    });

    test('初始化測試', () {
      expect(openRouter.serviceName, equals('OpenRouter'));
      expect(openRouter.defaultModelName, equals('qwen/qwen3-0.6b-04-28:free'));
      expect(openRouter.apiKeyUrl, equals('https://openrouter.ai/keys'));
    });

    test('initDio 測試', () {
      openRouter.initDio(testApiKey);
      expect(openRouter.dio, isNotNull);
      expect(openRouter.dio?.options.baseUrl, equals('https://openrouter.ai/api/v1'));
      expect(openRouter.dio?.options.headers['Authorization'], equals('Bearer $testApiKey'));
      expect(
        openRouter.dio?.options.headers['HTTP-Referer'],
        equals('https://github.com/windameister/adv_tarot_weaver'),
      );
      expect(openRouter.dio?.options.headers['X-Title'], equals('Advanced Tarot Weaver'));
    });

    test('roleCast 測試', () {
      expect(openRouter.roleCast(LlmRole.system), equals('system'));
      expect(openRouter.roleCast(LlmRole.assistant), equals('assistant'));
      expect(openRouter.roleCast(LlmRole.user), equals('user'));
    });

    test('getModels 測試 - 無網絡連接時的預設模型', () async {
      // 不提供有效的 API Key，測試預設模型列表
      final models = await openRouter.getModels('');
      expect(models, isNotEmpty);
    });

    // 以下測試需要有效的 API Key，如果沒有可以跳過或使用條件測試
    test('getModels 測試 - 有網絡連接', () async {
      // 跳過此測試，除非提供了有效的 API Key
      if (testApiKey == 'YOUR_API_KEY') {
        return;
      }

      final models = await openRouter.getModels(testApiKey);
      expect(models, isNotEmpty);
    });

    test('chat 測試 - 錯誤處理', () async {
      final messages = [const LlmMessage.user('Hello, world!')];

      // 使用無效的 API Key 測試錯誤處理
      final stream = openRouter.chat(apiKey: 'invalid_key', modelName: openRouter.defaultModelName, messages: messages);

      // 驗證錯誤處理
      await expectLater(stream, emitsAnyOf([contains('錯誤'), contains('Error')]));
    });

    test('chat 測試 - 有效請求', () async {
      // 跳過此測試，除非提供了有效的 API Key
      if (testApiKey == 'YOUR_API_KEY') {
        return;
      }

      final messages = [const LlmMessage.user('Hello, what is your name?')];

      final stream = openRouter.chat(apiKey: testApiKey, modelName: openRouter.defaultModelName, messages: messages);

      // 驗證是否有回應
      await expectLater(stream, emits(isNotNull));
    });
  });
}
