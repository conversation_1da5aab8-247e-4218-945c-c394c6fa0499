import 'dart:developer';

import 'package:flutter_test/flutter_test.dart';
import 'package:wu_llm/core/llm_gemini.dart';
import 'package:wu_llm/core/llm_message.dart';

void main() {
  late LlmGemini llmGemini;
  const String testApiKey = 'AIzaSyC-JG6NNEo-gH9HvqOz_CSjSPo5weHyrSI'; // 請替換為有效的 Gemini API Key
  const String testModelName = 'gemini-2.0-flash';

  setUp(() {
    llmGemini = LlmGemini();
    llmGemini.initDio(testApiKey);
  });

  group('LlmGemini 初始化測試', () {
    test('初始化應設置正確的服務名稱和預設模型', () {
      expect(llmGemini.serviceName, equals('Gemini'));
      expect(llmGemini.defaultModelName, equals('gemini-2.0-flash'));
      expect(llmGemini.apiKeyUrl, equals('https://aistudio.google.com/app/apikey'));
    });
  });

  group('getModels 測試', () {
    test('獲取模型列表', () async {
      final models = await llmGemini.getModels(testApiKey);

      // 驗證返回的模型列表不為空
      expect(models, isNotEmpty);

      // 驗證返回的模型列表包含預設模型
      expect(models, contains('gemini-2.0-flash'));

      // 打印獲取到的模型列表以供參考
      log('獲取到的模型列表: $models');
    });
  });

  group('roleCast 測試', () {
    test('角色轉換應正確映射', () {
      expect(llmGemini.roleCast(LlmRole.system), equals('user'));
      expect(llmGemini.roleCast(LlmRole.assistant), equals('model'));
      expect(llmGemini.roleCast(LlmRole.user), equals('user'));
    });
  });

  group('chat 測試', () {
    test('進行聊天', () async {
      // 創建測試訊息
      final messages = [const LlmMessage.system('你是一個有用的助手'), const LlmMessage.user('你好，請簡短回答')];

      // 啟動聊天流
      final chatStream = llmGemini.chat(apiKey: testApiKey, modelName: testModelName, messages: messages);

      // 收集響應
      final responseBuffer = StringBuffer();
      await for (final chunk in chatStream) {
        if (chunk != null) {
          responseBuffer.write(chunk);
          log('收到回應片段: $chunk');
        }
      }

      final fullResponse = responseBuffer.toString();
      log('完整回應: $fullResponse');

      // 驗證響應不為空
      expect(fullResponse, isNotEmpty);
    }, timeout: const Timeout(Duration(minutes: 2)));
  });

  group('錯誤處理測試', () {
    test('使用無效的 API Key', () async {
      // 使用明顯無效的 API Key
      const invalidApiKey = 'invalid_api_key';
      llmGemini.initDio(invalidApiKey);

      // 創建測試訊息
      final messages = [const LlmMessage.user('你好')];

      // 啟動聊天流
      final chatStream = llmGemini.chat(apiKey: invalidApiKey, modelName: testModelName, messages: messages);

      // 收集錯誤響應
      final responseList = await chatStream.toList();

      // 驗證有錯誤響應
      expect(responseList, isNotEmpty);
      log('錯誤響應: ${responseList.join()}');

      // 錯誤響應應該包含錯誤信息
      expect(responseList.join().contains('錯誤') || responseList.join().contains('error'), isTrue);
    });
  });

  group('streamText 和 futureText 測試', () {
    test('streamText 應返回流式響應', () async {
      final messages = [const LlmMessage.user('你好，請用一句話回答')];

      final stream = llmGemini.streamText(apiKey: testApiKey, modelName: testModelName, messages: messages);

      final result = StringBuffer();
      await for (final chunk in stream) {
        if (chunk != null) result.write(chunk);
      }
      log('streamText 響應: ${result.toString()}');
      expect(result, isNotEmpty);
    }, timeout: const Timeout(Duration(minutes: 1)));

    test('futureText 應返回完整響應', () async {
      final messages = [const LlmMessage.user('你好，請用一句話回答')];

      final response = await llmGemini.futureText(apiKey: testApiKey, modelName: testModelName, messages: messages);

      expect(response, isNotEmpty);
      log('futureText 響應: $response');
    }, timeout: const Timeout(Duration(minutes: 1)));
  });
}
