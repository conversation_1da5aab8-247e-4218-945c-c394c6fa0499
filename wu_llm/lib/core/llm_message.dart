/// LLM 角色枚舉
enum LlmRole { system, assistant, user }

/// LLM 訊息模型
class LlmMessage {
  final LlmRole role;
  final String content;

  const LlmMessage({required this.role, required this.content});

  /// 創建系統訊息
  const LlmMessage.system(String content) : this(role: LlmRole.system, content: content);

  /// 創建助手訊息
  const LlmMessage.assistant(String content) : this(role: LlmRole.assistant, content: content);

  /// 創建用戶訊息
  const LlmMessage.user(String content) : this(role: LlmRole.user, content: content);
}
