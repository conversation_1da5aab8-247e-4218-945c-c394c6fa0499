import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_service_base.dart';

/// Ollama 服務實現
class LlmOllama extends LlmServiceBase {
  static const String _baseUrl = 'http://localhost:11434/api';

  LlmOllama() : super(serviceName: 'Ollama', defaultModelName: 'llama3', apiKeyUrl: 'https://ollama.com/download');

  @override
  void initDio(String apiKey) {
    dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json'},
      ),
    );
  }

  @override
  Future<List<String>> getModels(String apiKey) async {
    if (dio == null) {
      initDio(apiKey);
    }

    final response = await dio?.get('/tags');
    if (response == null) return [];

    final data = response.data as Map<String, dynamic>;
    final models = (data['models'] as List).map((model) => model['name'] as String).toList();
    return models;
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
    LlmRole.system => 'system',
    LlmRole.user => 'user',
    LlmRole.assistant => 'assistant',
  };

  @override
  Stream<String?> chat({required String apiKey, required String modelName, required List<LlmMessage> messages}) async* {
    try {
      if (dio == null) {
        initDio(apiKey);
      }

      final response = await dio?.post(
        '/chat',
        data: {'model': modelName, 'messages': formatMessages(messages), 'stream': false},
      );

      if (response == null) {
        yield '錯誤：無法連接到 API';
        return;
      }

      final result = response.data['message']['content'];
      yield result ?? '';
    } catch (e) {
      yield handleError(e);
    }
  }
}
