import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_service_base.dart';

/// Gemini 服務實現
class LlmGemini extends LlmServiceBase {
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1';

  LlmGemini()
    : super(
        serviceName: 'Gemini',
        defaultModelName: 'gemini-2.0-flash',
        apiKeyUrl: 'https://aistudio.google.com/app/apikey',
      );

  @override
  void initDio(String apiKey) {
    dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json'},
        queryParameters: {'key': apiKey},
      ),
    );
  }

  @override
  Future<List<String>> getModels(String apiKey) async {
    if (dio == null) {
      initDio(apiKey);
    }

    final response = await dio?.get('/models');
    if (response == null) return [];

    final data = response.data as Map<String, dynamic>;
    final models = (data['models'] as List).map((model) => model['name'] as String).toList();
    for (var i = 0; i < models.length; i++) {
      if (models[i].startsWith('models/')) {
        models[i] = models[i].substring(7);
      }
    }
    return models;
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
    LlmRole.system => 'user',
    LlmRole.user => 'user',
    LlmRole.assistant => 'model',
  };

  @override
  List<Map<String, dynamic>> formatMessages(List<LlmMessage> messages) {
    return messages
        .map(
          (msg) => {
            'role': roleCast(msg.role),
            'parts': [
              {'text': msg.content},
            ],
          },
        )
        .toList();
  }

  @override
  Stream<String?> chat({required String apiKey, required String modelName, required List<LlmMessage> messages}) async* {
    try {
      if (dio == null) {
        initDio(apiKey);
      }

      // 使用 OpenAI 相容的 API 端點
      final response = await dio?.post(
        '/models/$modelName:generateContent',
        data: {
          'contents': formatMessages(messages),
          'generationConfig': {'temperature': 0.7, 'topK': 40, 'topP': 0.95, 'maxOutputTokens': 2048},
        },
      );

      if (response == null) {
        yield '錯誤：無法連接到 API';
        return;
      }

      final result = response.data['candidates'][0]['content']['parts'][0]['text'];
      yield result ?? '';
    } catch (e) {
      yield handleError(e);
    }
  }
}
