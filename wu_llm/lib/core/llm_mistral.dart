import 'dart:convert';
import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_service_base.dart';

/// Mistral 服務實現
class LlmMistral extends LlmServiceBase {
  static const String _baseUrl = 'https://api.mistral.ai/v1';

  LlmMistral()
    : super(
        serviceName: 'Mistral',
        defaultModelName: 'mistral-large',
        apiKeyUrl: 'https://console.mistral.ai/api-keys/',
      );

  @override
  void initDio(String apiKey) {
    dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json', 'Authorization': 'Bearer $apiKey'},
      ),
    );
  }

  @override
  Future<List<String>> getModels(String apiKey) async {
    if (dio == null) {
      initDio(apiKey);
    }

    final response = await dio?.get('/models');
    if (response == null) return [];

    final data = response.data as Map<String, dynamic>;
    final models = (data['data'] as List).map((model) => model['id'] as String).toList();
    return models;
  }

  @override
  String roleCast(LlmRole role) => role.toString().split('.').last;

  @override
  Stream<String?> chat({required String apiKey, required String modelName, required List<LlmMessage> messages}) async* {
    try {
      if (dio == null) {
        initDio(apiKey);
      }

      final response = await dio?.post(
        '/chat/completions',
        data: {'model': modelName, 'messages': formatMessages(messages), 'stream': true},
        options: Options(responseType: ResponseType.stream),
      );

      if (response == null) {
        yield '錯誤：無法連接到 API';
        return;
      }

      final stream = response.data.stream as Stream<List<int>>;
      String buffer = '';

      await for (final chunk in stream) {
        buffer += utf8.decode(chunk);
        final lines = buffer.split('\n');
        buffer = lines.last;

        for (final line in lines) {
          if (line.startsWith('data: ') && line != 'data: [DONE]') {
            final data = line.substring(6);
            try {
              final json = jsonDecode(data);
              final content = json['choices'][0]['delta']['content'] as String?;
              if (content != null) yield content;
            } catch (e) {
              // 忽略解析錯誤
            }
          }
        }
      }
    } catch (e) {
      yield handleError(e);
    }
  }
}
