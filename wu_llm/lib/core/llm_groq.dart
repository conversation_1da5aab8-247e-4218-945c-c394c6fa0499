import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_service_base.dart';

/// Groq 服務實現
class LlmGroq extends LlmServiceBase {
  static const String _baseUrl = 'https://api.groq.com/openai/v1';

  LlmGroq()
    : super(serviceName: 'Groq', defaultModelName: 'llama3-8b-8192', apiKeyUrl: 'https://console.groq.com/keys');

  @override
  void initDio(String apiKey) {
    dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json', 'Authorization': 'Bearer $apiKey'},
      ),
    );
  }

  @override
  Future<List<String>> getModels(String apiKey) async {
    if (dio == null) {
      initDio(apiKey);
    }
    final response = await dio?.get('/models');
    if (response == null) return [];

    final data = response.data as Map<String, dynamic>;
    final models = (data['data'] as List).map((model) => model['id'] as String).toList();
    return models;
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
    LlmRole.system => 'assistant',
    LlmRole.user => 'user',
    LlmRole.assistant => 'assistant',
  };

  @override
  Stream<String?> chat({required String apiKey, required String modelName, required List<LlmMessage> messages}) async* {
    try {
      if (dio == null) {
        initDio(apiKey);
      }

      final response = await dio?.post(
        '/chat/completions',
        data: {'model': modelName, 'messages': formatMessages(messages)},
      );

      if (response == null) {
        yield '錯誤：無法連接到 API';
        return;
      }

      final result = response.data['choices'][0]['message']['content'];
      yield result ?? '';
    } catch (e) {
      yield handleError(e);
    }
  }
}
