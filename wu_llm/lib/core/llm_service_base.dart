import 'package:dio/dio.dart';

import '../wu_llm.dart';

/// LLM 服務基礎抽象類
abstract class LlmServiceBase {
  /// 遠端 LLM 服務列表
  static const List<String> serviceList = [
    "Groq",
    "<PERSON>",
    "ChatGP<PERSON>",
    "<PERSON>",
    "Deepseek",
    "OpenRouter",
    // "Mistral",
    // "Perplexity",
    // "Cohere",
  ];

  static LlmServiceBase getService(String serviceName) {
    switch (serviceName) {
      case "ChatGPT":
        return LlmChatGpt();
      case "Gemini":
        return LlmGemini();
      case "Claude":
        return LlmClaude();
      case "Mistral":
        return LlmMistral();
      case "Groq":
        return LlmGroq();
      case "Perplexity":
        return LlmPerplexity();
      case "Deepseek":
        return LlmDeepseek();
      default:
        throw Exception("不支援的 LLM 服務：$serviceName");
    }
  }

  /// LLM 服務名稱
  final String serviceName;

  /// API 金鑰申請網址
  final String apiKeyUrl;

  /// 預設模型名稱
  final String defaultModelName;

  /// 發送超時時間
  final Duration sendTimeout;

  /// 接收超時時間
  final Duration receiveTimeout;

  /// 建構函數
  LlmServiceBase({
    required this.serviceName,
    required this.defaultModelName,
    this.apiKeyUrl = "",
    this.sendTimeout = const Duration(minutes: 5),
    this.receiveTimeout = const Duration(minutes: 10),
  });

  Dio? dio;

  /// 初始化 Dio
  void initDio(String apiKey);

  /// 獲取可用模型列表
  Future<List<String>> getModels(String apiKey);

  /// 聊天方法
  Stream<String?> chat({required String apiKey, required String modelName, required List<LlmMessage> messages});

  /// 流式文本生成
  Stream<String?> streamText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async* {
    yield* chat(apiKey: apiKey, modelName: modelName, messages: messages);
  }

  /// 非流式文本生成
  Future<String?> futureText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async {
    final result = StringBuffer();
    await for (final chunk in streamText(apiKey: apiKey, modelName: modelName, messages: messages)) {
      if (chunk != null) result.write(chunk);
    }
    return result.toString();
  }

  /// 將角色轉換為服務特定的字串格式
  String roleCast(LlmRole role) => role.toString().split('.')[1];

  /// 將訊息列表轉換為服務特定的格式
  List<Map<String, dynamic>> formatMessages(List<LlmMessage> messages) {
    final result = messages.map((msg) => {'role': roleCast(msg.role), 'content': msg.content}).toList();
    return result;
  }

  /// 錯誤處理方法
  String handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
          return '連接超時';
        case DioExceptionType.sendTimeout:
          return '發送請求超時';
        case DioExceptionType.receiveTimeout:
          return '接收響應超時';
        case DioExceptionType.badResponse:
          return '服務器響應錯誤：${error.response?.statusCode}';
        default:
          return '網絡錯誤：${error.message}';
      }
    }
    return '未知錯誤：$error';
  }
}
