import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_service_base.dart';

/// Claude 服務實現
class LlmClaude extends LlmServiceBase {
  static const String _baseUrl = 'https://api.anthropic.com/v1';

  LlmClaude()
    : super(
        serviceName: 'Claude',
        defaultModelName: 'claude-3-5-haiku-********',
        apiKeyUrl: 'https://console.anthropic.com/account/keys',
      );

  @override
  void initDio(String apiKey) {
    dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json', 'x-api-key': apiKey, 'anthropic-version': '2023-06-01'},
      ),
    );
  }

  @override
  Future<List<String>> getModels(String apiKey) async {
    try {
      if (dio == null) {
        initDio(apiKey);
      }

      final response = await dio?.get('/models');
      if (response == null) return [];

      final data = response.data as Map<String, dynamic>;
      final models = (data['data'] as List).map((model) => model['id'] as String).toList();
      return models;
    } catch (e) {
      return ['claude-3-7-sonnet-********'];
    }
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
    LlmRole.system => 'user',
    LlmRole.assistant => 'assistant',
    LlmRole.user => 'user',
  };

  @override
  Stream<String?> chat({required String apiKey, required String modelName, required List<LlmMessage> messages}) async* {
    try {
      if (dio == null) {
        initDio(apiKey);
      }

      // 準備請求資料
      final requestData = {
        'model': modelName,
        'messages': formatMessages(messages), // 只傳遞 user/assistant messages
        'stream': true,
        'max_tokens': 4096,
      };

      log("modelName: $modelName");
      final response = await dio?.post(
        '/messages',
        data: requestData, // 使用更新後的 requestData
        options: Options(responseType: ResponseType.stream),
      );

      if (response == null) {
        yield '錯誤：無法連接到 API';
        return;
      }

      final stream = response.data.stream as Stream<List<int>>;
      String buffer = '';

      await for (final chunk in stream) {
        buffer += utf8.decode(chunk);
        final lines = buffer.split('\n');
        buffer = lines.last;

        for (final line in lines) {
          if (line.startsWith('data: ')) {
            final data = line.substring(6);
            if (data == '[DONE]') continue;
            try {
              final json = jsonDecode(data);
              final content = json['delta']['text'] as String?;
              if (content != null) yield content;
            } catch (e) {
              // 忽略解析錯誤
            }
          }
        }
      }
    } catch (e) {
      yield handleError(e);
    }
  }
}
