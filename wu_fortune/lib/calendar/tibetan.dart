import 'package:tibetan_calendar/tibetan_calendar.dart';
import 'package:wu_core/public/chinese_number.dart';

class Tibetan {
  final monthInfo = {
    1: {"名稱": "神變月", "倍數": 100},
    2: {"名稱": "具實月", "倍數": 1000},
    3: {"名稱": "具香月", "倍數": 10000},
    4: {"名稱": "具花月", "倍數": 10000000000},
    6: {"名稱": "明淨月", "倍數": 10000000},
    9: {"名稱": "遊戲月", "倍數": 1000000000},
  };
  final dayInfo = {
    1: {"名稱": "等持如來", "倍數": 100},
    8: {"名稱": "藥師如來", "倍數": 1000},
    10: {"名稱": "賢劫千佛", "倍數": 100000},
    15: {"名稱": "阿彌陀佛", "倍數": 10000000},
    18: {"名稱": "觀世音菩薩", "倍數": 1000000},
    21: {"名稱": "地藏菩薩", "倍數": 100000000},
    25: {"名稱": "蓮華生大士", "倍數": 100000},
    30: {"名稱": "釋迦牟尼佛", "倍數": 900000000},
  };
  final int year, month, day;

  Tibetan({required this.year, required this.month, required this.day});
  factory Tibetan.bySolar(DateTime solar) {
    final tibetanDate = TibetanCalendar.getTibetanDate(solar);
    return Tibetan(year: tibetanDate.year, month: tibetanDate.month, day: tibetanDate.day);
  }
  String toStringCn() => "$year年$month月$day日";

  String? monthName() {
    final info = monthInfo[month];
    if (info == null) return null;
    return info["名稱"].toString();
  }

  String? dayName() {
    final info = dayInfo[day];
    if (info == null) return null;
    return "${info["名稱"]}";
  }

  String? multiple() {
    final infoM = monthInfo[month];
    final valM = infoM == null ? 1 : infoM["倍數"] as int;
    final infoD = dayInfo[day];
    final valD = infoD == null ? 1 : infoD["倍數"] as int;
    final val = valM * valD;
    if (val == 1) return null;
    return "行善功德 ${ChineseNumber.parse(val)} 倍";
  }
}
