import 'package:vnlunar/vnlunar.dart' as vnlunar;

import '../wu_calendar.dart';

class Lunar {
  final bool isLeap; // 是否閏月
  final int y, m, d, h;

  Zhi get gzY => GanZhi.byIndex(y - baseJiaziYear).zhi;
  String get zhY => ZODIAC[(y + 8) % 12];
  String get zhM => LUNAR_MONTHS.values.toList()[m - 1];
  String get zhD => LUNAR_DAYS[d - 1];

  Lunar({
    required this.y,
    required this.m,
    required this.d,
    required this.h,
    required this.isLeap,
  });

  factory Lunar.bySolar(DateTime dateTime) {
    var calTime = dateTime.add(const Duration(hours: 1));
    var lunar = vnlunar.Lunar(createdFromSolar: true, date: calTime);
    return Lunar(
      y: lunar.year,
      m: lunar.month,
      d: lunar.day,
      h: lunar.hour ~/ 2,
      isLeap: lunar.leapMonth ?? false,
    );
  }

  @override
  String toString() {
    var ret = "${toYMD()}${Zhi.byIndex(h)}時";
    return ret;
  }

  String toYMD() {
    var leap = (isLeap) ? "閏" : "";
    var ret = "$zhY年$leap$zhM月$zhD日";
    return ret;
  }
}
