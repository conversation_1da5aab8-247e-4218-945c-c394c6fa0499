import '../wu_zeri.dart';

class Zeri {
  DateTime solar;
  late GzDate gzdate;
  late List<String> goodHours;
  late String star12Mean;
  late List<String> fit;
  late List<String> unfit;

  Zeri(this.solar) {
    setDate(solar);
  }

  void nextDate() => setDate(solar.add(const Duration(days: 1)));
  void previousDate() => setDate(solar.add(const Duration(days: -1)));

  void setDate(DateTime solar) {
    print("[Zeri] setDate called with solar: $solar");
    this.solar = solar;
    gzdate = GzDate.bySolar(solar);
    print("[Zeri] Calculated gzdate: ${gzdate.toString()}");
    print("[Zeri DEBUG] gzdate.m.zhi.index: ${gzdate.m.zhi.index}");
    print("[Zeri DEBUG] gzdate.d.zhi.index: ${gzdate.d.zhi.index}");
    print("[Zeri DEBUG] gzdate.d.gan.index: ${gzdate.d.gan.index}");
    setStar12();
    setGoodHours1();
    setGoodHours2();
    setGoodHours3();
    print("[Zeri] setDate finished. goodHours: $goodHours");
  }

  /// 建除十二星
  void setStar12() {
    // 黃道吉日：除、危、定、執、成、開（吉）
    // 黑道吉日：建、滿、平、收、閉、破（凶）
    const items = "建除滿平定執破危成收開閉";

    // var index = (12 - _gzdate.m.zhi.index + _gzdate.d.zhi.index) % 12;
    var index = (12 - gzdate.m.zhi.index + gzdate.d.zhi.index) % 12;
    print(
      "[Zeri setStar12] month zhi index: ${gzdate.m.zhi.index}, day zhi index: ${gzdate.d.zhi.index}, calculated index: $index",
    );
    var mean = <String, String>{}, fit = <String, String>{}, unfit = <String, String>{};

    mean["成"] = "成日：成功、天帝紀萬物成就的大吉日子，凡事皆順。";
    fit["成"] = "結婚、開市、修造、動土、安床、破土、安葬、搬遷、交易、求財、出行、立契、豎柱、裁種、牧養";
    unfit["成"] = "訴訟";

    mean["收"] = "收日：收成、收穫，天帝寶庫收納的日子。";
    fit["收"] = "祈福、求嗣、赴任、嫁娶、安床、修造、動土、求學、開市、交易、買賣、立契";
    unfit["收"] = "放債、新船下水、新車下地、破土、安葬";

    mean["開"] = "開日：開始、開展的日子。";
    fit["開"] = "祭祀、祈福、入學、上任、修造、動土、開市、安床、交易、出行、豎柱";
    unfit["開"] = "放債、訴訟、安葬";

    // 次吉：吉日後，退而求其次的日子。
    mean["建"] = "建日：萬物生育、強健、健壯的日子。";
    fit["建"] = "赴任、祈福、求嗣、破土、安葬、修造、上樑、求財、置業、入學、考試、結婚、動土、簽約、交涉、出行";
    unfit["建"] = "動土、開倉、掘井、乘船、新船下水、新車下地、維修水電器具";

    mean["除"] = "除日：掃除惡煞、去舊迎新的日子。";
    fit["除"] = "祭祀、祈福、婚姻、出行、入伙、搬遷、出貨、動土、求醫、交易";
    unfit["除"] = "結婚、赴任、遠行、簽約";

    mean["滿"] = "滿日：豐收、美滿、天帝寶庫積滿的日子。";
    fit["滿"] = "嫁娶、祈福、移徙、開市、交易、求財、立契、祭祀、出行、牧養";
    unfit["滿"] = "造葬、赴任、求醫";

    // 平日：普通的日子。
    mean["平"] = "平日：平常、官人集合平分的日子。";
    fit["平"] = "嫁娶、修造、破土、安葬、牧養、開市、安床、動土、求嗣";
    unfit["平"] = "祈福、求嗣、赴任、嫁娶、開市、安葬";

    mean["定"] = "定日：安定、平常、天帝眾客定座的日子。";
    fit["定"] = "祭祀、祈福、嫁娶、造屋、裝修、修路、開市、入學、上任、入伙";
    unfit["定"] = "訴訟、出行、交涉";

    // 凶日：諸事不宜，最好避之則吉，喜事更可免則免。
    mean["執"] = "執日：破日之從神，曰小耗，天帝執行萬物賜天福，較差的日子。";
    fit["執"] = "造屋、裝修、嫁娶、收購、立契、祭祀";
    unfit["執"] = "開市、求財、出行、搬遷";

    mean["破"] = "破日：日月相衝，曰大耗，斗柄相衝相向必破壞的日子，大事不宜。";
    fit["破"] = "破土、拆卸、求醫";
    unfit["破"] = "嫁娶、簽約、交涉、出行、搬遷";

    mean["危"] = "危日：危機、危險，諸事不宜的日子。";
    fit["危"] = "祭祀、祈福、安床、拆卸、破土";
    unfit["危"] = "登山、乘船、出行、嫁娶、造葬、遷徙";

    mean["閉"] = "閉日：十二建中最後一日，關閉、收藏、天地陰陽閉寒的日子。";
    fit["閉"] = "祭祀、祈福、築堤、埋池、埋穴、造葬、填補、修屋";
    unfit["閉"] = "開市、出行、求醫、手術、嫁娶";

    var star12 = items[index];
    print("[Zeri setStar12] Calculated star12: $star12");
    star12Mean = mean[star12]!;
    this.fit = fit[star12]!.split("、");
    this.unfit = unfit[star12]!.split("、");
    print("[Zeri setStar12] Set star12Mean: $star12Mean, fit: ${this.fit}, unfit: ${this.unfit}");
  }

  String star28(DateTime solar) {
    // final items = "虛危室壁奎婁胃昴畢觜參井鬼柳星張翼軫角亢氐房心尾箕斗牛女";
    return "";
  }

  /// 喜神方
  /// 喜神方位歌
  /// 甲已在艮乙庚乾，丙辛坤位喜神安；
  /// 丁壬只在離宮坐，戊癸游來在巽間。
  String get god1Direction {
    var result = "喜神";
    if ("甲己".contains(gzdate.d.gan.name)) result += "東北";
    if ("乙庚".contains(gzdate.d.gan.name)) result += "西北";
    if ("丙辛".contains(gzdate.d.gan.name)) result += "西南";
    if ("丁壬".contains(gzdate.d.gan.name)) result += "南方";
    if ("戊癸".contains(gzdate.d.gan.name)) result += "東南";
    return result;
  }

  /// 福神方位歌
  /// 甲乙東南是福神，丙丁正東是堪宜；
  /// 戊北己南庚辛坤，壬在乾方癸在酉。
  String get god2Direction {
    var result = "福神";
    if ("甲乙".contains(gzdate.d.gan.name)) result += "東南";
    if ("丙丁".contains(gzdate.d.gan.name)) result += "東方";
    if ("戊".contains(gzdate.d.gan.name)) result += "北方";
    if ("己".contains(gzdate.d.gan.name)) result += "南方";
    if ("庚辛".contains(gzdate.d.gan.name)) result += "西南";
    if ("壬".contains(gzdate.d.gan.name)) result += "西北";
    if ("癸".contains(gzdate.d.gan.name)) result += "西方";
    return result;
  }

  /// 財神方位歌
  /// 甲乙東北是財神，丙丁向在西方尋；
  /// 戊己正北坐方位，庚辛正東去安身；
  /// 壬癸原來正南坐，便是財神方位真。
  String get god3Direction {
    var result = "財神";
    if ("甲乙".contains(gzdate.d.gan.name)) result += "東北";
    if ("丙丁".contains(gzdate.d.gan.name)) result += "西方";
    if ("戊己".contains(gzdate.d.gan.name)) result += "北方";
    if ("庚辛".contains(gzdate.d.gan.name)) result += "東方";
    if ("壬癸".contains(gzdate.d.gan.name)) result += "南方";
    return result;
  }

  void setGoodHours1() {
    print("[Zeri setGoodHours1] Initializing goodHours.");
    goodHours = List.generate(12, (index) => "平");

    void ganSetHour(bool isGood, String name, List<String> zhiList) {
      print("[Zeri ganSetHour] Setting for '$name', day gan: ${gzdate.d.gan.name} (index ${gzdate.d.gan.index})");
      var targetZhiNames = zhiList[gzdate.d.gan.index];
      print("[Zeri ganSetHour] Target zhi names string: '$targetZhiNames'");
      for (var zhiName in targetZhiNames.split("")) {
        print("[Zeri ganSetHour] Processing zhiName: '$zhiName'");
        var zhi1 = Zhi()..name = zhiName;
        print("[Zeri ganSetHour] Zhi object created: name='${zhi1.name}', index=${zhi1.index}");
        if (zhi1.index < 0) {
          goodHours[zhi1.index] = isGood ? "吉" : "凶";
          print("[Zeri ganSetHour] Set goodHours[${zhi1.index}] to ${goodHours[zhi1.index]}");
        } else {
          print("[Zeri ganSetHour] Warning: Invalid zhiName '$zhiName' resulted in null index. Skipping.");
        }
      }
    }

    void ganSetGanHour(bool isGood, List<String> ganList) {
      print("[Zeri ganSetGanHour] Setting for 五合時, day gan: ${gzdate.d.gan.name} (index ${gzdate.d.gan.index})");
      var targetGanNames = ganList[gzdate.d.gan.index];
      print("[Zeri ganSetGanHour] Target gan names string: '$targetGanNames'");
      for (var ganName in targetGanNames.split("")) {
        print("[Zeri ganSetGanHour] Processing target ganName: '$ganName'");
        var gan1 = Gan()..name = ganName;
        print("[Zeri ganSetGanHour] Target Gan object: name='${gan1.name}', index=${gan1.index}");
        if (gan1.index < 0) {
          print("[Zeri ganSetGanHour] Warning: Invalid target ganName '$ganName'. Skipping.");
          continue;
        }
        var startGanIndexOfHour = get5Mouse(gzdate.d.gan).index;
        for (var zidx = 0; zidx < 12; zidx++) {
          // 用五鼠遁查詢時干
          var currentHourGanIndex = (startGanIndexOfHour + zidx) % 10; // 時干索引
          print("[Zeri ganSetGanHour] Checking hour zhi index $zidx: hour gan index $currentHourGanIndex");
          // 這裡的邏輯似乎是檢查當前時辰的天干是否等於目標天干 gan1
          // 但原來的邏輯是比較 gidx == index，gidx 是時干索引+時支索引，index 是目標天干索引，這不對。
          // 應該是比較 currentHourGanIndex == gan1.index
          // 並且，如果是吉，應該設置 goodHours[zidx] = "吉"，而不是 goodHours[gan1.index]
          if (currentHourGanIndex == gan1.index) {
            print("[Zeri ganSetGanHour] Match found! Setting goodHours[$zidx] to ${isGood ? '吉' : '凶'}");
            goodHours[zidx] = isGood ? "吉" : "凶"; // 修正：設置對應時辰的吉凶
          }
        }
      }
    }

    void zhiSetHour(bool isGood, String name, List<String> zhiList) {
      print("[Zeri zhiSetHour] Setting for '$name', day zhi: ${gzdate.d.zhi.name} (index ${gzdate.d.zhi.index})");
      var targetZhiNames = zhiList[gzdate.d.zhi.index];
      print("[Zeri zhiSetHour] Target zhi names string: '$targetZhiNames'");
      for (var zhiName in targetZhiNames.split("")) {
        print("[Zeri zhiSetHour] Processing zhiName: '$zhiName'");
        // 特別注意：如果 zhiName 是空字串 ""，Zhi()..name = "" 會導致 index 為 null
        if (zhiName.isEmpty) {
          print("[Zeri zhiSetHour] Warning: Encountered empty zhiName. Skipping.");
          continue; // 跳過空字串
        }
        var zhi1 = Zhi()..name = zhiName;
        print("[Zeri zhiSetHour] Zhi object created: name='${zhi1.name}', index=${zhi1.index}");
        print("[Zeri DEBUG] zhiSetHour - zhiName: '$zhiName', zhi1.index: ${zhi1.index}");
        if (zhi1.index < 0) {
          goodHours[zhi1.index] = isGood ? "吉" : "凶";
          print("[Zeri zhiSetHour] Set goodHours[${zhi1.index}] to ${goodHours[zhi1.index]}");
        } else {
          print("[Zeri zhiSetHour] Warning: Invalid zhiName '$zhiName' resulted in null index. Skipping.");
        }
      }
    }

    // 天乙貴人，日干
    ganSetHour(true, "天乙貴人", ["丑未", "子申", "酉亥", "卯巳", "午寅", "丑未", "子申", "酉亥", "卯巳", "午寅"]);
    // 天官貴人，日干
    ganSetHour(true, "天官貴人", ["酉", "申", "子", "亥", "卯", "寅", "午", "巳", "丑未", "辰戌"]);
    // 福星貴人，日干
    ganSetHour(true, "福星貴人", ["寅", "丑亥", "子戌", "酉", "申", "未", "午", "巳", "辰", "卯"]);
    // 五合時，日干<<<<<<用時干對照
    ganSetGanHour(true, ["己", "庚", "辛", "壬", "癸", "甲", "乙", "丙", "丁", "戊"]);
    // 日祿時，日干
    ganSetHour(true, "日祿時", ["寅", "卯", "巳", "午", "巳", "午", "申", "酉", "亥", "子"]);
    // 五不遇時，日干
    ganSetHour(false, "五不遇時", ["午", "巳", "辰", "卯", "寅", "丑", "子", "酉", "申", "未"]);
    // 截路空亡時，日干
    ganSetHour(false, "截路空亡", ["申酉", "午未", "辰巳", "寅卯", "子丑戌亥", "申酉", "午未", "辰巳", "寅卯", "子丑戌亥"]);

    // 日建時，日支
    zhiSetHour(true, "日建時", ["子", "丑", "寅", "卯", "", "巳", "", "未", "申", "", "戌", ""]);
    // 六合時，日支
    zhiSetHour(true, "六合時", ["丑", "子", "亥", "戌", "酉", "申", "未", "午", "巳", "辰", "卯", "寅"]);
    // 三合時，日支
    zhiSetHour(true, "三合時", ["申辰", "巳酉", "午戌", "亥未", "申子", "酉丑", "寅戌", "亥卯", "子辰", "巳酉", "寅午", "卯未"]);
    // 驛馬時，日支
    zhiSetHour(true, "驛馬時", ["寅", "亥", "", "巳", "寅", "", "申", "巳", "", "亥", "申", ""]);
    // 日破時，日支
    zhiSetHour(false, "日破時", ["午", "未", "申", "酉", "戌", "亥", "子", "丑", "寅", "卯", "辰", "巳"]);
    // 日害時，日支
    zhiSetHour(false, "日害時", ["未", "午", "巳", "辰", "卯", "寅", "丑", "子", "亥", "戌", "酉", "申"]);
    // 日刑時，日支
    zhiSetHour(false, "日刑時", ["卯", "戌", "巳", "子", "辰", "申", "午", "丑", "寅", "酉", "未", "亥"]);
    // 損明時，日支
    zhiSetHour(false, "損明時", ["丑辰未戌", "寅卯", "申酉", "申酉", "寅卯", "亥子", "亥子", "寅卯", "巳午", "巳午", "寅卯", "丑辰未戌"]);
  }

  /// 貴人登天門
  void setGoodHours2() {
    const start1 = "戌亥子子戌亥酉酉申申";
    const start2 = "辰卯寅寅辰卯巳巳午午";
    var zhi1 = Zhi()..name = start1[gzdate.d.gan.index];
    var index1 = (zhi1.index + gzdate.m.zhi.index) % 12;
    print(
      "[Zeri setGoodHours2] Calculated index1: $index1 (zhi1 index: ${zhi1.index}, month zhi index: ${gzdate.m.zhi.index})",
    );
    var zhi2 = Zhi()..name = start2[gzdate.d.gan.index];
    print("[Zeri setGoodHours2] zhi2 name: ${zhi2.name}, index: ${zhi2.index}");
    var index2 = (zhi2.index + gzdate.m.zhi.index) % 12;
    print(
      "[Zeri setGoodHours2] Calculated index2: $index2 (zhi2 index: ${zhi2.index}, month zhi index: ${gzdate.m.zhi.index})",
    );
    goodHours[index1] = "吉";
    goodHours[index2] = "吉";
    print("[Zeri setGoodHours2] Set goodHours[$index1] and goodHours[$index2] to 吉");
  }

  /// 黃道、黑道十二吉凶神
  /// 吉神：司命、青龍、明堂、金匱、寶光、玉堂
  /// 凶神：勾陳、天刑、朱雀、白虎、天牢、玄武
  /// 喜忌：興工、動土、造葬、納采、嫁娶、訂盟、交易
  void setGoodHours3() {
    // final godList = ["司命", "勾陳", "青龍", "明堂", "天刑", "朱雀", "金匱", "寶光", "白虎", "玉堂", "天牢", "玄武"];
    // final isGoodList = [true, false, true, true, false, false, true, true, false, true, false, false];
    final dragon = ["申", "戌", "子", "寅", "午", "辰", "申", "戌", "子", "寅", "午", "辰"];
    var zhi1 = Zhi()..name = dragon[gzdate.d.gan.index];
    var index1 = (zhi1.index + gzdate.m.gan.index + 2) % 12; // 注意：這裡用了月干 gzdate.m.gan.index
    print(
      "[Zeri setGoodHours3] Calculated index1: $index1 (zhi1 index: ${zhi1.index}, month gan index: ${gzdate.m.gan.index})",
    );
    goodHours[index1] = "吉";
    print("[Zeri setGoodHours3] Set goodHours[$index1] to 吉");
  }
}
