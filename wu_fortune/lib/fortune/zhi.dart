import 'package:flutter/material.dart';

import '../wu_fortune.dart';

// （五）地支刑沖會合書破
// 三會：寅卯辰會木，巳午未會火，中酉戌會金，亥子丑會水。
// 三合：亥卯未合木，寅午戌合火，已酉丑合金，申子辰合水。
// 六合：子丑合化土，寅亥合化木，卯戌合化火，辰酉合化金，巳申合化水，午未合化火，為日月之合，共六組，故稱六合。
// 六沖：子午沖、丑未沖、寅申沖、卯酉沖、辰戌沖、巳亥沖，共六組，故稱六沖。
// 相刑：寅巳刑、寅申刑、巳申刑、丑未刑、丑戌刑、未戌刑、子卯刑，辰辰自刑、午午自刑、酉酉自刑、亥亥自刑。
// 六害：子未害、丑午書、寅巳害、卯辰害、申亥害、酉戌害，共六組，故稱六害。
// 相破：子酉破、午卯破、申巳破、寅亥破、辰丑破、戌未破。
// （六）地支刑沖會合香破之意涵
// 刑：是悶氣。
// 沖：是衝動、很衝、對立。
// 會合：是團結、有情。
// 害破：是抱怨、怨歎。
class Zhi extends Noun implements IWuxingYY {
  static final dicts = [
    ("子", "水", "鼠"),
    ("丑", "土", "牛"),
    ("寅", "木", "虎"),
    ("卯", "木", "兔"),
    ("辰", "土", "龍"),
    ("巳", "火", "蛇"),
    ("午", "火", "馬"),
    ("未", "土", "羊"),
    ("申", "金", "猴"),
    ("酉", "金", "雞"),
    ("戌", "土", "狗"),
    ("亥", "水", "豬"),
  ];
  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = dicts.map((e) => e.$1).toList();
    return _names;
  }

  Zhi() : super(names);
  factory Zhi.byIndex(int index) => Zhi()..index = index;
  factory Zhi.byName(String name) => Zhi()..name = name;
  int get hour => (index * 2 + 23) % 24;
  int get hourM => (index * 2) % 24;
  String get zodiac => dicts[index].$3;
  Color get color => wuxing.color;

  @override
  Wuxing get wuxing => Wuxing.byName(dicts[index].$2);

  @override
  bool get isYang => index % 2 == 0;
}
