import 'package:flutter/material.dart';

import '../wu_fortune.dart';

abstract class IWuxing {
  Wuxing get wuxing;
}

abstract class <PERSON><PERSON>uxingYY extends IWuxing {
  bool get isYang;
}

class Wuxing extends Noun {
  static final dicts = [
    ("木", Colors.green),
    ("火", Colors.red),
    ("土", Colors.brown),
    ("金", Colors.amber),
    ("水", Colors.blue),
  ];
  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = dicts.map((e) => e.$1).toList();
    return _names;
  }

  Wuxing() : super(names);
  factory Wuxing.byIndex(int index) => Wuxing()..index = index;
  factory Wuxing.byName(String name) => Wuxing()..name = name;

  Color get color => dicts[index].$2;
}
