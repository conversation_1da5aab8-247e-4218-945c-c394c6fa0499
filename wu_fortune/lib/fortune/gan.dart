import 'package:flutter/material.dart';

import '../wu_fortune.dart';

/// 天干
class Gan extends Noun implements IWuxingYY {
  static final dicts = [
    ("甲", "木"),
    ("乙", "木"),
    ("丙", "火"),
    ("丁", "火"),
    ("戊", "土"),
    ("己", "土"),
    ("庚", "金"),
    ("辛", "金"),
    ("壬", "水"),
    ("癸", "水"),
  ];

  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = dicts.map((e) => e.$1).toList();
    return _names;
  }

  Gan() : super(names);
  factory Gan.byIndex(int index) => Gan()..index = index;
  factory Gan.byName(String name) => Gan()..name = name;

  /// 五行顏色
  Color get color => wuxing.color;

  @override
  Wuxing get wuxing => Wuxing.byName(dicts[index].$2);

  @override
  bool get isYang => index % 2 == 0;
}
// 十干祿能加強十天干旺度，其口訣為甲祿在寅，乙祿在卯，丙戊祿在巳，丁己祿在午，庚祿在申，辛祿在酉，壬祿在亥，癸祿在子。
