import 'package:wu_core/extensions/string_extension.dart';

import '../wu_fortune.dart';

class Gua8 extends Noun implements IWuxingYY {
  // 納甲装卦歌（從下裝起）
  // 乾金甲子外壬午，子寅辰午申戌
  // 兌金丁巳外丁亥，已卯丑亥酉未
  // 離火已卯外己酉，卯丑亥酉未已
  // 震木庚子外庚午，子寅辰午申戌
  // 巽木辛丑外辛未，丑亥酉未已卯
  // 坎水戊寅外戊申，寅辰午申戌子
  // 艮土丙辰外丙戌，辰午申戌子寅
  // 坤土乙未外癸丑，未已卯丑亥酉
  static final dicts = [
    ("乾", "金", "天", "---", "子寅辰午申戌", "西北", "午", false),
    ("兌", "金", "澤", "=--", "巳卯丑亥酉未", "正西", "辰巳", false),
    ("離", "火", "火", "-=-", "卯丑亥酉未巳", "正南", "卯", false),
    ("震", "木", "雷", "==-", "子寅辰午申戌", "正東", "丑寅", true),
    ("巽", "木", "風", "--=", "丑亥酉未巳卯", "東南", "未申", true),
    ("坎", "水", "水", "=-=", "寅辰午申戌子", "正北", "酉", true),
    ("艮", "土", "山", "-==", "辰午申戌子寅", "東北", "戌亥", true),
    ("坤", "土", "地", "===", "未巳卯丑亥酉", "西南", "子", false),
  ];
  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = dicts.map((e) => e.$1).toList();
    return _names;
  }

  Gua8() : super(names);
  factory Gua8.byIndex(int index) => Gua8()..index = index;
  factory Gua8.byName(String name) => Gua8()..name = name;
  factory Gua8.bySign(String sign) => Gua8()..index = dicts.indexWhere((e) => e.$4 == sign);

  /// 象
  String get xiang => dicts[index].$3;

  /// 符號
  String get sign => dicts[index].$4;

  /// 佈地支
  String get layout => dicts[index].$5.reverse();

  /// 方位
  String get direction => dicts[index].$6;

  /// 地支
  String get zhis => dicts[index].$7;

  @override
  Wuxing get wuxing => Wuxing.byName(dicts[index].$2);

  @override
  bool get isYang => dicts[index].$8;
}
