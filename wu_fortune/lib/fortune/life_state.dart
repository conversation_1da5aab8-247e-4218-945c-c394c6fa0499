import '../wu_fortune.dart';

/// 十二長生
class LifeState extends Noun {
  static final dicts = [
    ("長", "長生"),
    ("沐", "沐浴"),
    ("冠", "冠帶"),
    ("臨", "臨官"),
    ("帝", "帝旺"),
    ("衰", "衰"),
    ("病", "病"),
    ("死", "死"),
    ("墓", "墓"),
    ("絕", "絕"),
    ("胎", "胎"),
    ("養", "養死"),
  ];
  static List<String> get names => dicts.map((e) => e.$1).toList();

  LifeState() : super(names);
  factory LifeState.byIndex(int index) => LifeState()..index = index;
  factory LifeState.byName(String name) => LifeState()..name = name;

  factory LifeState.byWuxing(IWuxingYY test, String zhiName, {bool sameDir = true}) {
    // 甲乙木:亥，丙丁火:寅，戊己土：寅，庚辛金：巳，壬癸水：申
    const start = {"木": 11, "火": 2, "土": 2, "金": 5, "水": 8};
    final zhi = Zhi.byName(zhiName);
    final offset = (test.isYang == false && sameDir == false)
        ? start[test.wuxing.name]! - zhi.index
        : start[test.wuxing.name]! + zhi.index;
    return LifeState.byIndex(offset);
  }

  factory LifeState.byGan(Gan test, String zhiName, {bool sameDir = false}) {
    Map<String, String> map;
    if (sameDir) {
      // 陰陽同長生
      map = {
        "甲": "亥", "乙": "亥", "丙": "寅", "丁": "寅", "戊": "寅", //
        "己": "寅", "庚": "巳", "辛": "巳", "壬": "申", "癸": "申", //
      };
    } else {
      // 陰陽異長生
      map = {
        "甲": "亥", "乙": "午", "丙": "寅", "丁": "酉", "戊": "寅", //
        "己": "酉", "庚": "巳", "辛": "子", "壬": "申", "癸": "卯", //
      };
    }
    // 異長生，且為陰時逆向
    final direction = !((sameDir == false) && (test.isYang == false));
    final zhi1 = Zhi.byName(map[test.name]!);
    final zhi2 = Zhi.byName(zhiName);
    var offset = direction ? (zhi2.index - zhi1.index) : (zhi1.index - zhi2.index);
    return LifeState.byIndex(offset);
  }

  /// 長生十二神
  String get longName => dicts[index].$2;

  /// 以陰陽測試
  static String testWX(IWuxingYY test, Gua8 gong, {bool sameDir = true}) {
    var ret = "";
    for (var zhi in gong.zhis.split("")) {
      ret += LifeState.byWuxing(test, zhi, sameDir: sameDir).name;
    }
    return ret;
  }

  /// 以天干測試
  static String testGan(Gan gan, Gua8 gong, {bool sameDir = false}) {
    var ret = "";
    for (var zhi in gong.zhis.split("")) {
      ret += LifeState.byGan(gan, zhi, sameDir: sameDir).name;
    }
    return ret;
  }

  @override
  String toString() => name;
}
