/// 名詞抽象類別
/// 定義了名詞的基本屬性和方法
abstract class Noun {
  /// 名稱列表
  final List<String> _names;
  Noun(this._names);

  /// 索引
  int _index = -1;
  int get index => _index;
  set index(int value) {
    final len = _names.length;
    _index = ((value % len) + len) % len;
  }

  /// 名稱
  set name(String value) {
    _index = _names.indexOf(value);
    if (_index < 0) {
      throw ArgumentError("名稱不存在");
    }
  }

  String get name {
    if (_names.isEmpty) return "null";
    if (_index < 0 || _index >= _names.length) return "null";
    return _names[_index];
  }

  @override
  String toString() => name;
}
