import '../wu_fortune.dart';

String strzero(dynamic input, int width) {
  return input.toString().padLeft(width, '0');
}

/// 修正索引
int fixIndex(int index, int count) {
  while (index < 0) {
    index += count;
  }
  return index % count;
}

/// 五鼠遁
/// 甲己還加甲——逢日干是甲或己的日子，子時的時干從甲上起！
/// 乙庚丙作初——逢日干是乙或庚的日子，子時的時干從丙上起！
/// 丙辛從戊起——逢日干是丙或辛的日子，子時的時干從戊上起！
/// 丁壬庚子居——逢日干是丁或壬的日子，子時的時干從庚上起！
/// 戊癸起壬子——逢日干是戊或癸的日子，子時的時干從壬上起！
Gan get5Mouse(Gan parentGan) {
  return Gan.byIndex(parentGan.index % 5 * 2);
}

/// 五虎遁
/// 甲己之年丙作首——逢年干是甲或己的年份，正月的月干從丙上起！
/// 乙庚之歲戊為頭——逢年干是乙或庚的年份，正月的月干從戊上起！
/// 丙辛必定尋庚起——逢年干是丙或辛的年份，正月的月干從庚上起！
/// 丁壬壬位順行流——逢年干是丁或壬的年份，正月的月干從壬上起！
Gan get5Tiger(Gan parentGan) {
  return Gan.byIndex(parentGan.index % 5 * 2);
}
