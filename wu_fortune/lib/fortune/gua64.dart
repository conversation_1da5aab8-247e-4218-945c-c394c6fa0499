import '../wu_fortune.dart';

class Gua64 extends Noun {
  static final dicts = [
    //
    ("乾乾", "乾", "八純", "天"),
    ("乾巽", "乾", "一世", "姤"),
    ("乾艮", "乾", "二世", "遯"),
    ("乾坤", "乾", "三世", "否"),
    ("巽坤", "乾", "四世", "觀"),
    ("艮坤", "乾", "五世", "剝"),
    ("離坤", "乾", "遊魂", "晉"),
    ("離乾", "乾", "歸魂", "大有"),
    //
    ("離離", "離", "八純", "火"),
    ("離艮", "離", "一世", "旅"),
    ("離巽", "離", "二世", "鼎"),
    ("離坎", "離", "三世", "未濟"),
    ("艮坎", "離", "四世", "蒙"),
    ("巽坎", "離", "五世", "渙"),
    ("乾坎", "離", "遊魂", "訟"),
    ("乾離", "離", "歸魂", "同人"),
    //
    ("兌兌", "兌", "八純", "澤"),
    ("兌坎", "兌", "一世", "困"),
    ("兌坤", "兌", "二世", "萃"),
    ("兌艮", "兌", "三世", "咸"),
    ("坎艮", "兌", "四世", "蹇"),
    ("坤艮", "兌", "五世", "謙"),
    ("震艮", "兌", "遊魂", "小過"),
    ("震兌", "兌", "歸魂", "歸妹"),
    //
    ("震震", "震", "八純", "雷"),
    ("震坤", "震", "一世", "豫"),
    ("震坎", "震", "二世", "解"),
    ("震巽", "震", "三世", "恒"),
    ("坤巽", "震", "四世", "升"),
    ("坎巽", "震", "五世", "井"),
    ("兌巽", "震", "遊魂", "大過"),
    ("兌震", "震", "歸魂", "隨"),
    //
    ("巽巽", "巽", "八純", "風"),
    ("巽乾", "巽", "一世", "小畜"),
    ("巽離", "巽", "二世", "家人"),
    ("巽震", "巽", "三世", "益"),
    ("乾震", "巽", "四世", "無妄"),
    ("離震", "巽", "五世", "噬嗑"),
    ("艮震", "巽", "遊魂", "頤"),
    ("艮巽", "巽", "歸魂", "蠱"),
    //
    ("坎坎", "坎", "八純", "水"),
    ("坎兌", "坎", "一世", "節"),
    ("坎震", "坎", "二世", "屯"),
    ("坎離", "坎", "三世", "既濟"),
    ("兌離", "坎", "四世", "革"),
    ("震離", "坎", "五世", "豐"),
    ("坤離", "坎", "遊魂", "明夷"),
    ("坤坎", "坎", "歸魂", "師"),
    //
    ("艮艮", "艮", "八純", "山"),
    ("艮離", "艮", "一世", "賁"),
    ("艮乾", "艮", "二世", "大畜"),
    ("艮兌", "艮", "三世", "損"),
    ("離兌", "艮", "四世", "睽"),
    ("乾兌", "艮", "五世", "履"),
    ("巽兌", "艮", "遊魂", "中孚"),
    ("巽艮", "艮", "歸魂", "漸"),
    //
    ("坤坤", "坤", "八純", "地"),
    ("坤震", "坤", "一世", "復"),
    ("坤兌", "坤", "二世", "臨"),
    ("坤乾", "坤", "三世", "泰"),
    ("震乾", "坤", "四世", "大壯"),
    ("兌乾", "坤", "五世", "夬"),
    ("坎乾", "坤", "遊魂", "需"),
    ("坎坤", "坤", "歸魂", "比"),
  ];
  static List<String> get names => dicts.map((e) => e.$1).toList();

  Gua64() : super(names);
  factory Gua64.byIndex(int index) => Gua64()..index = index;
  factory Gua64.byName(String name) => Gua64()..name = name;
  factory Gua64.bySign6(String sign6) {
    // sign6 = sign6.reverse();
    final gua8u = Gua8.bySign(sign6.substring(0, 3));
    final gua8d = Gua8.bySign(sign6.substring(3, 6));
    return Gua64.byName("${gua8u.name}${gua8d.name}");
  }

  /// 卦名稱
  String get gong => dicts[index].$2;

  /// 大衍之數
  String get dai => dicts[index].$3;

  /// 象
  String get xiang => dicts[index].$4;

  /// 全名稱
  String get fullname {
    final x1 = Gua8.byName(name[0]);
    final x2 = Gua8.byName(name[1]);
    return (dai == "八純") ? "${x1.name}為$xiang" : "${x1.xiang}${x2.xiang}$xiang";
  }

  /// 兩卦
  List<Gua8> get gua8s => [Gua8.byName(name[0]), Gua8.byName(name[1])];

  /// 六爻符號
  String get sign6 => "${gua8s[0].sign}${gua8s[1].sign}";

  /// 佈地支
  List<String> get layout {
    return "${gua8s[0].layout.substring(0, 3)}${gua8s[1].layout.substring(3, 6)}".split('');
  }
}
