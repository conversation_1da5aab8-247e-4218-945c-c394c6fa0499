import '../wu_fortune.dart';

class <PERSON><PERSON><PERSON><PERSON> extends Noun {
  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = List.generate(60, (index) => "${Gan.byIndex(index).name}${Zhi.byIndex(index).name}");
    return _names;
  }

  GanZhi() : super(names);
  factory GanZhi.byIndex(int index) => GanZhi()..index = index;
  factory GanZhi.byName(String name) => GanZhi()..name = name;

  /// 設定干支
  void setGanzhi(int ganIndex, int zhiIndex) {
    // 干支必須干支同陰陽，所以使用查表法
    final g = Gan.byIndex(ganIndex);
    final z = Zhi.byIndex(zhiIndex);
    name = "${g.name}${z.name}";
  }

  /// 天干
  Gan get gan => Gan.byIndex(index);

  /// 地支
  Zhi get zhi => Zhi.byIndex(index);

  /// 旬首: 甲子、甲戌、甲申、甲午、甲辰、甲寅
  String get xunshou => "甲${Zhi.byIndex(zhi.index - gan.index).name}";

  /// 旬空: 甲子戌亥、甲戌申酉、甲申午未、甲午辰巳、甲辰寅卯、甲寅子丑
  String get xunkong {
    final xs = GanZhi.byName(xunshou);
    return "${Zhi.byIndex(xs.zhi.index + 10).name}${Zhi.byIndex(xs.zhi.index + 11).name}";
  }

  /// 符頭: 甲子戊、甲戌己、甲申庚、甲午辛、甲辰壬、甲寅癸
  String get futou {
    const dict = {"甲子": "戊", "甲戌": "己", "甲申": "庚", "甲午": "辛", "甲辰": "壬", "甲寅": "癸"};
    return dict[xunshou]!;
  }
}
