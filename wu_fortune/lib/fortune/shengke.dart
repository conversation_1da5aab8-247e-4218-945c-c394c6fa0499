import '../wu_fortune.dart';

class ShengKe extends Noun {
  static final dicts = [
    ("比合", "旺", "上", "兄", "用", 4),
    ("我生", "囚", "凶", "孫", "泄", 1),
    ("我尅", "休", "平", "財", "仇", 2),
    ("尅我", "廢", "下", "官", "忌", 0),
    ("生我", "相", "吉", "父", "原", 3),
  ];
  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = dicts.map((e) => e.$1).toList();
    return _names;
  }

  ShengKe() : super(names);
  factory ShengKe.byIndex(int index) => ShengKe()..index = index;
  factory ShengKe.byName(String name) => ShengKe()..name = name;
  factory ShengKe.byCompare(IWuxing wxFrom, IWuxing wxTo) {
    return ShengKe.byIndex(wxFrom.wuxing.index - wxTo.wuxing.index);
  }

  /// 旺相
  String get wangxiang => dicts[index].$2;

  /// 吉凶
  String get jixiong => dicts[index].$3;

  /// 六親
  String get liuqin => dicts[index].$4;

  /// 原忌
  String get yuanji => dicts[index].$5;

  /// 分數
  int get score => dicts[index].$6;
}
