import '../wu_fortune.dart';

/// 八字十神
class Shishen {
  static const dicts = [
    <PERSON><PERSON>n("比", "比肩", "比合同", {
      "意義": "重感情、重朋友、熱心大方、獨立、喜愛幫助別人。比肩多者，好勝心強、意志堅定、不喜歡受拘束、主觀意識強、自尊心也強、具有競爭力，個性上較不通融。",
      "心性": "穩健剛毅，勇敢冒險，積極進取，但易流於孤僻，缺乏合羣，反為孤立寡合。",
      "職能": "財之敵",
    }),
    <PERSON>shen("劫", "劫財", "比合異", {
      "意義": "反應好、口才不錯、野心大、不服輸、喜歡交朋友、重感情、通融性佳、損財、競爭、見風轉舵，外表樂觀、內心想不開。劫財多者，常因為朋友而勞碌、也因為容易受他人影響到才運與感情、沒有安全感、容易貪小便宜（因小失大）。",
      "心性": "熱誠坦直，堅韌志旺，奮鬥不屈，但易流於盲目，缺乏理智，反為蠻橫衝動。",
      "職能": "財之敵",
    }),
    <PERSON>she<PERSON>("食", "食神", "我生同", {
      "意義": "代表我的想法、聰明才智；在個性上表現比較傳統、厚道、有藝術音樂天份。食神多容易想的多，也較鑽牛角尖，常會有比較不切實際的想法，有口福和口慾、愛美、樂觀進取、溫文儒雅、接受傳統。",
      "心性": "温文隨和，帶人寬厚，善良體貼，但易流於虛偽，缺乏是非，反為迂腐懦弱。",
      "職能": "傷見官仗勢欺之，放任日干於理法之外，故一般以凶神論；食見殺則能制服，使日干得以安然無故，一般以吉神論。",
    }),
    Shishen("傷", "傷官", "我生異", {
      "意義": "也是我的想法、聰明才智、學習能力強、個性叛逆、不服輸、不喜修邊幅、愛好自由。傷官過多，容易一意孤行、不守禮教、講話犀利、挑剔、沖犯長上、管夫、特立獨行、易被感動、潑辣、自尊心強、清秀姣好、任性、多愁善感。",
      "心性": "聰明活躍，才華橫溢，逞強好勝，但易流於任性，缺乏約束，反為桀驁不馴。",
      "職能": "傷見官仗勢欺之，放任日干於理法之外，故一般以凶神論；食見殺則能制服，使日干得以安然無故，一般以吉神論。",
    }),
    Shishen("才", "偏財", "我尅同", {
      "意義": "具有掌控和掌握的欲望，慷慨豪邁、疏財重義、為目的不擇手段、具競爭力、用情不專。偏財過多，物慾高、易做投機性的投資、好勝心強、自私。",
      "心性": "慷慨重情，聰明機靈，樂觀開朗，但易流於虛浮，缺乏節制，反為浮華風流。",
      "職能": "是養命之源，人人所欲，但非人人所得，古今皆然。一般以吉神論。",
    }),
    Shishen("財", "正財", "我尅異", {
      "意義": "個性表現上比較保守、守舊、謹慎、正派、道德觀、滿於現狀、固定財源、占有慾也強、勤儉、儲蓄。正財過多，會比較小氣計較，堅持執著，也會比較現實。",
      "心性": "勤勞節儉，踏實保守，任勞任怨，但易流於苟且，缺乏進取，反為懦弱無能。",
      "職能": "是養命之源，人人所欲，但非人人所得，古今皆然。一般以吉神論。",
    }),
    Shishen("殺", "七殺", "尅我同", {
      "意義": "八字七殺表壓力、災難、小人、觀察力好、比較敏感、積極有創造力、熱心、有正義感、責任感、愛面子、很有開創與領導的性格、報復心、敵對計謀。七殺多者，個性會比較剛烈、暴躁、叛逆與固執、專制霸道。",
      "心性": "豪爽俠義，積極進取，威嚴機敏，但易流於偏激，叛逆霸道，反為墮落極端。",
      "職能": "殺身之對手，專以攻身為尚，若無禮法制裁，必傷其主（日干），故有制（有食神，傷官剋制）謂之偏官，無制謂之七殺。一般以凶神論。",
    }),
    Shishen("官", "正官", "尅我異", {
      "意義": "因為是剋我，所以跟七殺一樣具有壓力、規範，個性表現上比較壓抑和約束、拘謹、有紀律、負責任、沉著穩重、守信、保守有禮。正官多者，比較膽小怕事、優柔寡斷、固執不易溝通。",
      "心性": "正直負責，端莊嚴肅，循規蹈矩，但易流於刻板，墨守成規，反為意志不堅。",
      "職能": "是善意之管，譬如人類，必須遵從政府與法律管束。正官一般以吉神論。",
    }),
    Shishen("梟", "偏印", "生我同", {
      "意義": "代表神秘、獨樹一格、頑固、奇招異數、驚覺多疑、偏執、聰明刁鑽、同中求異。對於哲學思想和宗教信仰有興趣，重視內在思考，也可代表福氣或貴；偏印多者會比較沒有安全感、內心較孤僻、孤獨。",
      "心性": "精明幹練，反應機敏，多才多藝，但易流於孤獨，缺乏人情，反為自私冷漠。",
      "職能": "我之氣源，如父母生身之義。正印一般為吉神，偏印一般為凶神，又稱梟神，見食（食神）而奪為梟神之故。",
    }),
    Shishen("印", "正印", "生我異", {
      "意義": "具有同情心、仁厚、仁慈、文學文藝氣息、自命清高、依賴的個性、喜異中求同、有宗教信仰和修持的機緣、重精神層面。正印過多，會比較懶散、依賴、過於自我及固執，而因過於博愛而吃虧。",
      "心性": "聰穎仁慈，淡泊名利，逆來順受，但易流於庸碌，缺乏進取，反為遲鈍消極。",
      "職能": "我之氣源，如父母生身之義。正印一般為吉神，偏印一般為凶神，又稱梟神，見食（食神）而奪為梟神之故。",
    }),
  ];

  final String short;
  final String name;
  final String rule;
  final Map<String, String> p;

  const Shishen(this.short, this.name, this.rule, this.p);

  /// 以兩個含陰陽項目比較取得十神
  factory Shishen.byCompare(IWuxingYY from, IWuxingYY to) {
    final sameas = from.isYang == to.isYang ? "同" : "異";
    final shengke = ShengKe.byCompare(from, to);
    final key = shengke.name + sameas;
    final ret = dicts.firstWhere((x) => x.rule == key);
    return ret;
  }

  /// 找出正財偏財
  static Gan getCai(String shishen, Gan gan) {
    final dict = {"木": "戊己", "火": "庚辛", "土": "壬癸", "金": "甲乙", "水": "丙丁"};
    final val = dict[gan.wuxing.name]!;
    final gans = [Gan.byName(val[0]), Gan.byName(val[1])];
    // "我尅同": {"全": "偏財", "簡": "才"},
    // "我尅異": {"全": "正財", "簡": "財"},
    var ret = gan.isYang == gans[0].isYang ? gans[0] : gans[1];
    if (shishen == "正財") {
      ret = gan.isYang == gans[0].isYang ? gans[1] : gans[0];
    }
    return ret;
  }
}
