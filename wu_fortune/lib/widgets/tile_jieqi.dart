import 'package:flutter/material.dart';
import 'package:wu_core/wu_extensions.dart';

import '../wu_calendar.dart';

class TileJieqi extends StatelessWidget {
  final int year;
  final double textScale;
  const <PERSON><PERSON><PERSON><PERSON><PERSON>({super.key, required this.year, this.textScale = 1.0});

  @override
  Widget build(BuildContext context) {
    return Table(
      border: TableBorder.all(color: Colors.grey),
      columnWidths: {
        0: FlexColumnWidth(2),
        1: FlexColumnWidth(2),
        2: FlexColumnWidth(3),
        3: FlexColumnWidth(2),
        4: FlexColumnWidth(3),
      },
      children: List.generate(12, (month) {
        final monthStr = zhMonthFull[month];
        final jq1 = Jieqi.byYearIndex(year, month * 2);
        final jq2 = Jieqi.byYearIndex(year, month * 2 + 1);
        return TableRow(children: [
          Padding(
            padding: const EdgeInsets.all(2.0),
            child: Text(
              monthStr,
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(2.0),
            child: Text(
              jq1.name,
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(2.0),
            child: Text(
              jq1.dateTime.md(),
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(2.0),
            child: Text(
              jq2.name,
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(2.0),
            child: Text(
              jq2.dateTime.md(),
              textScaler: TextScaler.linear(textScale),
              textAlign: TextAlign.center,
            ),
          ),
        ]);
      }),
    );
  }
}
