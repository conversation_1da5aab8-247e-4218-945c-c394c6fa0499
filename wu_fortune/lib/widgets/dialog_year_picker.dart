import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DialogYearPicker extends StatefulWidget {
  final int startYear;
  final int endYear;
  final int selectedYear;

  const DialogYearPicker({super.key, required this.startYear, required this.endYear, required this.selectedYear});

  @override
  State<DialogYearPicker> createState() => _DialogYearPickerState();
}

class _DialogYearPickerState extends State<DialogYearPicker> {
  late int _currentYear;
  late final ScrollController _scrollController;
  final double _itemHeight = 48.0;
  final int _itemsPerRow = 3;

  @override
  void initState() {
    super.initState();
    _currentYear = widget.selectedYear;
    _scrollController = ScrollController();

    // 使用 Future.delayed 確保在下一幀渲染後執行滾動
    Future.delayed(Duration(milliseconds: 300), () {
      _scrollToSelectedYear();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToSelectedYear() {
    if (!_scrollController.hasClients) return;

    // 計算選中年份在列表中的位置
    final index = _currentYear - widget.startYear;
    final rowIndex = index ~/ _itemsPerRow;

    // 計算滾動位置
    final rowHeight = _itemHeight + 8.0; // 項目高度 + 間距
    final offset = rowIndex * rowHeight;

    // 確保滾動位置在有效範圍內
    final maxScrollExtent = _scrollController.position.maxScrollExtent;
    final safeOffset = offset.clamp(0.0, maxScrollExtent);

    // 使用 jumpTo 直接滾動到目標位置
    _scrollController.jumpTo(safeOffset);
  }

  @override
  Widget build(BuildContext context) {
    final years = List.generate(widget.endYear - widget.startYear + 1, (index) => widget.startYear + index);

    return Dialog(
      child: Container(
        width: 300,
        padding: const EdgeInsets.all(16),
        constraints: BoxConstraints(maxWidth: 300), // 添加最大寬度限制
        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部标题和返回按钮
            Row(
              children: [
                IconButton(icon: Icon(Icons.arrow_back), onPressed: () => Navigator.of(context).pop()),
                Expanded(
                  child: Text(
                    "選擇年份",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(width: 48), // 平衡布局
              ],
            ),
            // 年份网格
            SizedBox(
              height: 300, // 限制高度
              child: GridView.builder(
                controller: _scrollController,
                padding: EdgeInsets.all(12),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: _itemsPerRow,
                  mainAxisSpacing: 8,
                  crossAxisSpacing: 8,
                  mainAxisExtent: _itemHeight,
                ),
                itemCount: years.length,
                itemBuilder: (context, index) {
                  final year = years[index];
                  final isSelected = year == _currentYear;

                  return InkWell(
                    onTap: () {
                      Get.back(result: year);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.blue[100] : null,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: isSelected ? Colors.blue : Colors.grey[300]!, width: 1),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        year.toString(),
                        style: TextStyle(
                          color: isSelected ? Colors.blue[900] : Colors.black87,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
