import 'package:flutter/material.dart';
import 'package:wu_core/wu_extensions.dart';

import '../wu_qimen.dart';
import '../wu_widgets.dart';

/// 奇門開盤基本資訊
class QimenHeaderView extends StatelessWidget {
  final QimenDesk desk;
  const QimenHeaderView({super.key, required this.desk});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [Expanded(flex: 1, child: _buildDateCol()), Expanded(flex: 1, child: _buildQimenCol())],
      ),
    );
  }

  Widget _buildQimenCol() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("起局：${desk.p['陰陽遁']}${desk.p['局']}局"),
        Text("旬首：${desk.p['旬首']}(${desk.p['符頭']})"),
        Text("空亡：${desk.p['空亡']}"),
        Text("值符：${desk.p['值符']}"),
        Text("值使：${desk.p['值使']}"),
        Text("天乙：${desk.p['天乙']}"),
      ],
    );
  }

  Widget _buildDateCol() {
    // final solar = desk.solar;
    final gzDate = desk.gzdate;
    // 伏吟、反吟
    final ffyin = [desk.fuyin(), desk.fanyin()].join(" ").trim();
    // 五不遇時、天輔時、時干入墓
    final (stName, stMean) = specialTime(gzDate);
    final stColor = stName == "天輔時" ? Colors.red : Colors.green;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("起盤：${desk.p['定局']}"),
        Text("公曆：${desk.solar.ymdhm()}"),
        TileGzdate(gzDate: gzDate),
        if (ffyin.isNotEmpty) Text(ffyin, style: TextStyle(color: Colors.green)),
        if (stName?.isNotEmpty ?? false) Text(stName!, style: TextStyle(color: stColor)),
      ],
    );
  }
}
