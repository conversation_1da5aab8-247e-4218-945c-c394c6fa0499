// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:wu_fortune/wu_widgets.dart';

import '../wu_qimen.dart';

class QimenDeskView extends StatelessWidget {
  final QimenDesk desk;
  final double itemSize;
  final Color colorLucky;
  final Color colorMoney;
  final Color colorBad1;
  final Color colorBad2;
  QimenDeskView({
    super.key,
    required this.desk,
    this.itemSize = 22,
    this.colorLucky = const Color.fromARGB(255, 106, 37, 37),
    this.colorMoney = const Color.fromARGB(255, 152, 116, 0),
    this.colorBad1 = const Color.fromARGB(255, 53, 141, 56),
    this.colorBad2 = const Color.fromARGB(255, 21, 101, 166),
  });

  final borderSide = BorderSide(color: Colors.grey, width: 1);
  TextStyle textStyle = TextStyle();

  @override
  Widget build(BuildContext context) {
    textStyle = TextStyle(height: 1.0, fontSize: itemSize * 0.8);
    return ConstrainedBox(
      constraints: BoxConstraints(minWidth: 300, maxWidth: 450),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final cellWidth = (constraints.maxWidth * 0.9) / 3;
          return Table(
            border: TableBorder.all(),
            children: [
              TableRow(
                children: [
                  _buildCell(desk.cells[3], cellWidth),
                  _buildCell(desk.cells[4], cellWidth),
                  _buildCell(desk.cells[5], cellWidth),
                ],
              ),
              TableRow(
                children: [
                  _buildCell(desk.cells[2], cellWidth),
                  _buildCell(desk.cells[8], cellWidth),
                  _buildCell(desk.cells[6], cellWidth),
                ],
              ),
              TableRow(
                children: [
                  _buildCell(desk.cells[1], cellWidth),
                  _buildCell(desk.cells[0], cellWidth),
                  _buildCell(desk.cells[7], cellWidth),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCell(QimenCell cell, double cellWidth) {
    return Container(
      width: cellWidth,
      height: cellWidth,
      alignment: Alignment.topCenter,
      child: Table(
        children: [
          TableRow(children: [_buildPos00(cell), _buildPos01(cell), _buildPos02(cell)]),
          TableRow(children: [_buildPos10(cell), _buildPos11(cell), _buildPos12(cell)]),
          TableRow(children: [_buildPos20(cell), _buildPos21(cell), _buildPos22(cell)]),
        ],
      ),
    );
  }

  /// 後天八卦、洛書
  Widget _buildPos00(QimenCell cell) {
    if (cell.serial == 0) return Container(height: itemSize);
    return ItemBox(
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.symmetric(horizontal: 1),
      child: Text("${cell.p["後天"]}${cell.p["洛書"]}", style: textStyle),
    );
  }

  /// 八神
  Widget _buildPos01(QimenCell cell) {
    if (cell.serial == 0) return Container(height: itemSize);
    return ItemBox(alignment: Alignment.center, child: Text("${cell.p["神"]}", style: textStyle));
  }

  /// 驛馬、旬空
  Widget _buildPos02(QimenCell cell) {
    if (cell.serial == 0) return Container(height: itemSize);
    var text = '';
    if (cell.p["馬"] ?? false) text += "🐎";
    if (cell.p["空"] ?? false) text += "🈳";
    return ItemBox(
      alignment: Alignment.centerRight,
      padding: EdgeInsets.symmetric(horizontal: 1),
      child: Text(text, style: textStyle),
    );
  }

  /// 寄天
  Widget _buildPos10(QimenCell cell) {
    if (cell.serial == 0) return Container(height: itemSize);
    final gan1 = cell.p["寄天"] ?? "";
    return ItemBox(
      alignment: Alignment.centerLeft,
      child: GanRender(gan1, foreBad: cell.fourBad(gan1), style: textStyle),
    );
  }

  /// 八門
  Widget _buildPos11(QimenCell cell) {
    if (cell.serial == 0) return Container(height: itemSize);
    final fb = cell.fourBad(cell.p["門"]);
    var boxColor = Colors.black;
    if (fb.contains('迫')) boxColor = colorBad1;
    if (fb.contains('制')) boxColor = colorBad2;

    return ItemBox(
      alignment: Alignment.center,
      child: Text("${cell.p["門"]}", style: textStyle.copyWith(color: boxColor)),
    );
  }

  /// 天盤
  Widget _buildPos12(QimenCell cell) {
    if (cell.serial == 0) return Container(height: itemSize);
    final gan2 = cell.p["天盤"] ?? "";
    return ItemBox(
      alignment: Alignment.centerRight,
      child: GanRender(gan2, foreBad: cell.fourBad(gan2), style: textStyle),
    );
  }

  /// 引干
  Widget _buildPos20(QimenCell cell) {
    if (cell.serial == 0) return Container(height: itemSize);
    final gan1 = cell.p["引干"] ?? "";
    return ItemBox(
      alignment: Alignment.centerLeft,
      child: GanRender(gan1, foreBad: cell.fourBad(gan1), style: textStyle),
    );
  }

  /// 九星
  Widget _buildPos21(QimenCell cell) {
    if (cell.serial == 0) return Container(height: itemSize);
    return ItemBox(alignment: Alignment.center, child: Text("${cell.p["星"]}", style: textStyle));
  }

  /// 寄地、地盤
  Widget _buildPos22(QimenCell cell) {
    final gan2 = cell.p["地盤"] ?? "";
    return ItemBox(
      alignment: Alignment.centerRight,
      child: GanRender(gan2, foreBad: cell.fourBad(gan2), style: textStyle),
    );
  }
}

class ItemBox extends StatelessWidget {
  final AlignmentGeometry alignment;
  final double height;
  final Widget child;
  final EdgeInsetsGeometry padding;
  const ItemBox({
    super.key,
    required this.child,
    this.alignment = Alignment.center,
    this.height = 22,
    this.padding = const EdgeInsets.all(0),
  });

  @override
  Widget build(BuildContext context) {
    return Container(height: height, margin: EdgeInsets.all(1), padding: padding, alignment: alignment, child: child);
  }
}
