import 'package:flutter/material.dart';
import 'package:wu_core/wu_extensions.dart';

import '../wu_fortune.dart';

class ZhiRender extends StatelessWidget {
  final String zhiName;
  final double textScale;
  const ZhiRender({super.key, required this.zhiName, this.textScale = 1.0});

  @override
  Widget build(BuildContext context) {
    final zhi = Zhi.byName(zhiName);
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            zhiName,
            textScaler: TextScaler.linear(textScale),
            style: TextStyle(fontSize: 20, height: 1.0),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                zhi.hour.strzero(2),
                textScaler: TextScaler.linear(textScale),
                style: TextStyle(fontSize: 9, height: 1.0),
              ),
              Text(
                zhi.hourM.strzero(2),
                textScaler: TextScaler.linear(textScale),
                style: TextStyle(fontSize: 9, height: 1.0),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
