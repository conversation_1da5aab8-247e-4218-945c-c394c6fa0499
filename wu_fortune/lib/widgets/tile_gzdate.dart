import 'package:flutter/material.dart';

import '../calendar/gzdate.dart';

class TileGzdate extends StatelessWidget {
  final GzDate gzDate;
  const TileGzdate({super.key, required this.gzDate});

  @override
  Widget build(BuildContext context) {
    final gzStyle = TextStyle(fontSize: 18);
    return Table(
      border: TableBorder.all(),
      children: [
        TableRow(
          children: [
            Text("年", textAlign: TextAlign.center),
            Text("月", textAlign: TextAlign.center),
            Text("日", textAlign: TextAlign.center),
            Text("時", textAlign: TextAlign.center),
          ],
        ),
        TableRow(
          children: [
            Text(gzDate.y.gan.name, style: gzStyle.copyWith(color: gzDate.y.gan.color), textAlign: TextAlign.center),
            Text(gzDate.m.gan.name, style: gzStyle.copyWith(color: gzDate.m.gan.color), textAlign: TextAlign.center),
            Text(gzDate.d.gan.name, style: gzStyle.copyWith(color: gzDate.d.gan.color), textAlign: TextAlign.center),
            Text(gzDate.h.gan.name, style: gzStyle.copyWith(color: gzDate.h.gan.color), textAlign: TextAlign.center),
          ],
        ),
        TableRow(
          children: [
            Text(gzDate.y.zhi.name, style: gzStyle.copyWith(color: gzDate.y.zhi.color), textAlign: TextAlign.center),
            Text(gzDate.m.zhi.name, style: gzStyle.copyWith(color: gzDate.m.zhi.color), textAlign: TextAlign.center),
            Text(gzDate.d.zhi.name, style: gzStyle.copyWith(color: gzDate.d.zhi.color), textAlign: TextAlign.center),
            Text(gzDate.h.zhi.name, style: gzStyle.copyWith(color: gzDate.h.zhi.color), textAlign: TextAlign.center),
          ],
        ),
      ],
    );
  }
}
