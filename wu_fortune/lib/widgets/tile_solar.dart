import 'package:flutter/material.dart';
import 'package:wu_core/wu_extensions.dart';
import 'package:wu_core/wu_public.dart';

import '../wu_calendar.dart';

class TileSolar extends StatelessWidget {
  final DateTime solar;
  const TileSolar({super.key, required this.solar});

  @override
  Widget build(BuildContext context) {
    final jieqi = Jieqi.bySolar(solar);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InfoRow(left: '公曆：', right: "${solar.ymd()} (${zhWeek[solar.weekday % 7]})"),
        InfoRow(left: '節氣：', right: "${jieqi.name} ${jieqi.dateTime.md()}"),
      ],
    );
  }
}
