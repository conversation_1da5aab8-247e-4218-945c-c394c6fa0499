import 'package:flutter/material.dart';

class GanRender extends StatelessWidget {
  final String ganName;
  final String? foreBad;
  final List<int> intensity;
  final AlignmentGeometry alignment;
  final TextStyle style;
  const GanRender(
    this.ganName, {
    super.key,
    this.foreBad,
    this.intensity = const [],
    this.alignment = Alignment.center,
    this.style = const TextStyle(height: 1.0, color: Colors.black),
  });

  @override
  Widget build(BuildContext context) {
    final backColor = foreBad?.contains('墓') ?? false ? Colors.brown.shade200 : Colors.transparent;
    final borderColor = foreBad?.contains('刑') ?? false ? Colors.blue : Colors.transparent;
    final boxSize = style.fontSize! + 4;
    if (ganName.isEmpty) return Container();
    return Container(
      margin: const EdgeInsets.all(1),
      decoration: BoxDecoration(
        color: backColor,
        border: Border.all(color: borderColor, width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
      height: boxSize,
      width: boxSize,
      alignment: alignment, // 將文字置中
      child: Text(ganName, style: style),
    );
  }
}
