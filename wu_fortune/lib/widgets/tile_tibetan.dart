import 'package:flutter/material.dart';
import 'package:wu_core/wu_public.dart';

import '../wu_calendar.dart';

class TileTibetan extends StatelessWidget {
  final DateTime solar;
  final Color colorLucky;
  const TileTibetan({super.key, required this.solar, this.colorLucky = Colors.red});

  @override
  Widget build(BuildContext context) {
    final tibetan = Tibetan.bySolar(solar);
    var tn = tibetan.monthName();
    if (tn != null) tn += tibetan.dayName() ?? '';
    if (tn != null) tn += "功德日";
    final tm = tibetan.multiple();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InfoRow(left: "藏曆：", right: tibetan.toStringCn()),
        if (tn?.isNotEmpty ?? false) Text(tn ?? '', style: TextStyle(color: colorLucky)),
        if (tm?.isNotEmpty ?? false) Text(tm ?? '', style: TextStyle(color: colorLucky)),
      ],
    );
  }
}
