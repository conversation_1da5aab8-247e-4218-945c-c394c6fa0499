import '../wu_qimen.dart';

class Qimen {
  static const cnum = '一二三四五六七八九';
  static const gongs = '乾兌離震巽坎艮坤';
  static const gans = '戊己庚辛壬癸丁丙乙';
  static const gods = {'符': '值符', '蛇': '騰蛇', '陰': '太陰', '六': '六合', '白': '白虎', '玄': '玄武', '地': '九地', '天': '九天'};
  static const stars = '篷任沖輔英芮柱心';
  static const doors = '休生傷杜景死驚開';
  static const wuxing = '水土木木火土金金';

  static String? badSymbolHandle(String nurture, String symbol) {
    // 宮位四害，該宮如有該物品就請移走，否則就需制化
    final items = {
      '乾': {
        '乙': '木雕、窗、藝術品、葫蘆',
        '丙': '灶、紅色、圓形物、眼鏡',
        '戊': '古董、陶製品、舊物品',
        '景': '手機、電視、合照、電腦、證書、合同、電器、紅色',
      },
      '坎': {
        '生': '廚房調料、生活浴室用品',
        '死': '醫療器材、遺照、玩具、雕塑品、繩索、木偶、刀劍',
      },
      '艮': {
        '丁': '燈、小刀、手機、首飾、小花',
        '己': '線團、垃圾、洗手間',
        '庚': '金屬製品、健身器材',
        '傷': '利器、破損、殘缺東西',
        '杜': '內衣、口罩、報章雜誌',
      },
      '震': {
        '戊': '古董、舊物品、鍋碗瓢盆',
        '驚': '電視、音響、樂器、聲響',
        '開': '錢包、球類、時鐘、珠寶',
      },
      '巽': {
        '辛': '金銀、珠寶、刀具、金屬',
        '壬': '清潔用品、水機、水圖',
        '癸': '茶酒醋、垃圾、鞋、雨具',
        '驚': '電視、音響、樂器、聲響',
        '開': '錢包、球類、時鐘、珠寶',
      },
      '離': {
        '辛': '刀、鑰匙、珠寶、水晶、酒',
        '休': '馬達、鬆軟物、涼椅、鐘錶、液體、休閒器材、電風扇',
      },
      '坤': {
        '符': '金銀、珠寶、財字、福字',
        '癸': '雨具、鞋、茶、酒、醋、水',
        '己': '線團、垃圾、洗手間',
        '傷': '利器、破損、殘缺東西',
        '杜': '內衣、口罩、報章雜誌',
      },
      '兌': {'景': '手機、電視、合照、電腦、證書、合同、電器、紅色'},
    };
    return items[nurture]?[symbol];
  }
}

class QimenDesk {
  late List<QimenCell> cells = [];
  final Map<String, dynamic> p = {};

  static const numsDef = ["1 6", "5 7 8 10", "3 4 8", "3 4 5 8", "2 3 7 9", "2 5 8 10", "2 4 7 9", "1 4 6 9"];
  static const numsYe = ["1 6", "5", "3 4", "4 5 8", "7 9", "2 5 8", "2 4", "6 9"];

  /// 設置預設宮位訊息
  QimenDesk() {
    const nums = numsYe;
    cells = [
      QimenCell(this, 1, 1, "坤", "坎", "篷", "休", nums[0], "子"),
      QimenCell(this, 2, 8, "震", "艮", "任", "生", nums[1], "丑寅"),
      QimenCell(this, 3, 3, "離", "震", "沖", "傷", nums[2], "卯"),
      QimenCell(this, 4, 4, "兌", "巽", "輔", "杜", nums[3], "辰巳"),
      QimenCell(this, 5, 9, "乾", "離", "英", "景", nums[4], "午"),
      QimenCell(this, 6, 2, "巽", "坤", "芮", "死", nums[5], "未申"),
      QimenCell(this, 7, 7, "坎", "兌", "柱", "驚", nums[6], "酉"),
      QimenCell(this, 8, 6, "艮", "乾", "心", "開", nums[7], "戌亥"),
      QimenCell(this, 0, 5, "中", "中", "", "", "", ""),
    ];
  }

  DateTime get solar => p['公曆'] as DateTime;
  GzDate get gzdate => p['干支曆'] as GzDate;
  String get futou => p['符頭'] as String;
  String get xunshou => p['旬首'] as String;

  List<QimenCell>? cellWhere(String propName, dynamic propValue) {
    if (propName == '天') {
      return cells.where((c) => c.p['天盤'] == propValue || c.p['寄天'] == propValue).toList();
    }
    if (propName == '地') {
      return cells.where((c) => c.p['地盤'] == propValue || c.p['寄地'] == propValue).toList();
    }
    return cells.where((c) => c.p[propName] == propValue).toList();
  }

  /// noCenter: 不會選擇中宮
  QimenCell? cellFirst(String propName, dynamic propValue, {bool noCenter = false}) {
    QimenCell? cell;
    if (propName == '天') {
      cell ??= cells.firstWhere((c) => c.p['天盤'] == propValue || c.p['寄天'] == propValue);
    }
    if (propName == '地') {
      cell ??= cells.firstWhere((c) => c.p['地盤'] == propValue || c.p['寄地'] == propValue);
    }
    if (propValue == '甲') propValue = futou;
    cell ??= cells.firstWhere((c) => c.p.containsKey(propName) && c.p[propName] == propValue);

    if (noCenter && cell.luoshu == 5) cell = cellLuoshu(2);
    return cell;
  }

  QimenCell cellKeyFirst(String propValue) {
    if (propValue == '甲') propValue = futou;
    if (propValue == '值符') propValue = p['值符'];
    if (propValue == '值使') propValue = p['值使'];
    if (propValue == '日干') propValue = gzdate.d.gan.name;
    if (propValue == '時干') propValue = gzdate.h.gan.name;
    return cells.firstWhere((c) => c.keyProps.contains(propValue));
  }

  int rangeInt(int min, int max, int val) {
    final rng = max - min + 1;
    val -= min;
    while (val < 0) {
      val += rng;
    }
    val %= rng;
    val += min;
    return val;
  }

  QimenCell cellSerial(int serial) {
    serial = rangeInt(1, 8, serial); // 1-8
    return cellFirst('順序', serial)!;
  }

  QimenCell cellLuoshu(int luoshu) {
    luoshu = rangeInt(1, 9, luoshu);
    return cellFirst('洛書', luoshu)!;
  }

  String fuyin() {
    var ret = '';
    if (cells[0].p['星'] == cells[0].p['原星']) ret += '星';
    if (cells[0].p['門'] == cells[0].p['原門']) ret += '門';
    if (cells[0].p['天盤'] == cells[0].p['地盤']) ret += '干';
    if (ret.length == 3) ret = '全';
    if (ret.isNotEmpty) ret += '伏吟';

    return ret;
  }

  String fanyin() {
    var ret = '';
    if (cells[0].p['星'] == cells[4].p['原星']) ret += '星';
    if (cells[0].p['門'] == cells[4].p['原門']) ret += '門';
    if (cells[0].p['天盤'] == cells[4].p['地盤']) ret += '干';
    if (ret.length == 3) ret = '全';
    if (ret.isNotEmpty) ret += '反吟';
    return ret;
  }

  /// 翻宮（未來）地盤轉天盤
  int moveForward(int luoshu) {
    var cell = cellLuoshu(luoshu);
    cell = cellFirst("天盤", cell.p["地盤"])!;
    return cell.luoshu;
  }

  /// 轉宮（過去）天盤轉地盤
  int moveBackward(int luoshu) {
    var cell = cellLuoshu(luoshu);
    cell = cellFirst("地盤", cell.p["天盤"])!;
    return cell.luoshu;
  }

  /// 深挖=>先天的後天位
  int moveDeep(int luoshu) {
    var cell = cellLuoshu(luoshu);
    cell = cellFirst("後天", cell.p["先天"])!;
    return cell.luoshu;
  }

  /// 飄移=>後天的先天位
  int moveShift(int luoshu) {
    var cell = cellLuoshu(luoshu);
    cell = cellFirst("先天", cell.p["後天"])!;
    return cell.luoshu;
  }
}
