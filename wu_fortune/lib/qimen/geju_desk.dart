import 'package:wu_fortune/wu_qimen.dart';

const luckySet1 = ["天遁", "地遁", "人遁", "雲遁", "風遁", "龍遁", "虎遁", "神遁"];
const luckySet2 = ["真詐", "重詐", "休詐"];
const luckySet3 = ["權怡", "相佐"];

typedef DeskRule = bool Function(QimenDesk desk);

/// 全盤格局
class GejuDesk {
  final String category;
  final String title;
  final String? mean;
  final String? comment;
  final DeskRule rule;

  GejuDesk({
    required this.category,
    required this.title,
    required this.rule,
    this.mean,
    this.comment,
  });

  static List<GejuDesk> getList(QimenDesk desk) {
    return _gejuDesks.where((p) => p.rule.call(desk)).toSet().toList();
  }
}

final _gejuDesks = [
  // 出門：開休生+景，乙丙丁符首，符六天陰地，輔任沖心，
  // "青龍返首", "飛鳥跌穴", "玉女守門", "天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁","真詐","重詐","休詐"
  GejuDesk(
    category: "出門",
    title: "出門", // Assuming title is same as category if not specified
    comment: "開休生+景，乙丙丁符首，符六天陰地，輔任沖心",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("開休生景").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 求醫：生+開休，乙丙丁符首，輔心任沖，符天地陰六
  // "青龍返首", "飛鳥跌穴", "天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁","真詐","重詐","休詐", "權怡", "相佐"
  GejuDesk(
    category: "求醫",
    title: "求醫",
    comment: "生+開休，乙丙丁符首，輔心任沖，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("開休生").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 買賣：休景傷開生，乙丙丁符首，輔心任沖，符天地陰六
  // "青龍返首", "飛鳥跌穴", "玉女守門", "天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁","真詐","重詐","休詐", "天假","權怡", "相佐"
  GejuDesk(
    category: "買賣",
    title: "買賣",
    comment: "休景傷開生，乙丙丁符首，輔心任沖，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("休景傷開生").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 求財：休生開，乙丙丁符首，輔心任沖，符天地陰六
  // "青龍返首", "飛鳥跌穴", "天遁","雲遁","風遁","龍遁","虎遁","神遁","真詐","重詐","休詐", "權怡", "相佐"
  GejuDesk(
    category: "求財",
    title: "求財",
    comment: "休生開，乙丙丁符首，輔心任沖，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("休生開").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 祈福：休生開，甲乙丙丁，輔心任沖，符天地陰六
  // "飛鳥跌穴","天遁","地遁","雲遁","風遁","龍遁","虎遁","神遁","真詐","重詐","休詐","神假","權怡","相佐,"天輔時格"
  GejuDesk(
    category: "祈福",
    title: "祈福",
    comment: "休生開，甲乙丙丁，輔心任沖，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.xunshou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("休生開").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 考試：景休生開，甲乙丙丁，輔心任沖，符天地陰六
  // "青龍返首", "飛鳥跌穴", "天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁","真詐","重詐","休詐"
  GejuDesk(
    category: "考試",
    title: "考試",
    comment: "景休生開，甲乙丙丁，輔心任沖，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.xunshou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("景休生開").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 社交：景休生開，甲乙丙丁，輔心任沖，符天地陰六（景：提升地位，開：突破難關，休：解除煩惱，生：生生不息）
  // "青龍返首", "飛鳥跌穴", "天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁","真詐","重詐","休詐"
  GejuDesk(
    category: "社交",
    title: "社交",
    comment: "景休生開，甲乙丙丁，輔心任沖，符天地陰六（景：提升地位，開：突破難關，休：解除煩惱，生：生生不息）",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.xunshou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("景休生開").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 隱密：杜，符首乙丙丁，輔心沖任英柱芮，符天地陰六
  // "地遁","人遁","鬼遁","天假","地假","人假","神假","鬼假"
  GejuDesk(
    category: "隱密",
    title: "隱密",
    comment: "杜，符首乙丙丁，輔心沖任英柱芮，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("杜").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 競賽：開生，乙丙丁符首，輔心任沖，符天地陰六
  // "青龍返首", "飛鳥跌穴", "玉女守門","天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁","真詐","重詐","休詐","權怡","相佐"
  GejuDesk(
    category: "競賽",
    title: "競賽",
    comment: "開生，乙丙丁符首，輔心任沖，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("開生").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 訴訟：景開驚傷杜休，乙丙丁符首，輔心任沖，符天地陰六（景開：辯論勝訴，驚：嚇唬對方，傷：攻擊對方，杜：消除災害，休：和解）
  GejuDesk(
    category: "訴訟",
    title: "訴訟",
    comment: "景開驚傷杜休，乙丙丁符首，輔心任沖，符天地陰六（景開：辯論勝訴，驚：嚇唬對方，傷：攻擊對方，杜：消除災害，休：和解）",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("景開驚傷杜休").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 求偶：景休開生，乙丙丁符首，輔心任沖英，符天地陰六
  // "青龍返首", "飛鳥跌穴", "玉女守門","天遁","地遁","人遁","神遁","鬼遁","天假"
  GejuDesk(
    category: "求偶",
    title: "求偶",
    comment: "景休開生，乙丙丁符首，輔心任沖英，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("景休開生").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 婚姻：景休開生，乙丙丁符首，輔心任沖英，符天地陰六
  // "青龍返首", "飛鳥跌穴", "玉女守門","天遁","地遁","人遁","天假","真詐","重詐","休詐","權怡"
  GejuDesk(
    category: "婚姻",
    title: "婚姻",
    comment: "景休開生，乙丙丁符首，輔心任沖英，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("景休開生").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 廣告：景休開生，甲乙丙丁，輔心任沖英，符天地陰六
  // "青龍返首", "飛鳥跌穴", "玉女守門","天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁","天假","真詐","重詐","休詐","權怡","相佐"
  GejuDesk(
    category: "廣告",
    title: "廣告",
    comment: "景休開生，甲乙丙丁，輔心任沖英，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.xunshou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("景休開生").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 談判：傷，甲乙丙丁，輔心任沖英，符天地陰六
  GejuDesk(
    category: "談判",
    title: "談判",
    comment: "傷，甲乙丙丁，輔心任沖英，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.xunshou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("傷").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 討債：傷，甲乙丙丁，輔心任沖英，符天地陰六
  GejuDesk(
    category: "討債",
    title: "討債",
    comment: "傷，甲乙丙丁，輔心任沖英，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.xunshou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("傷").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 陽宅：景休開生，符首乙丙丁，輔心任沖英，符天地陰六
  // "青龍返首","飛鳥跌穴","三奇得使","三奇昇旺"
  // "天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁",
  // "天假","真詐","重詐","休詐",
  // "權怡","相佐"
  GejuDesk(
    category: "陽宅",
    title: "陽宅",
    comment: "景休開生，符首乙丙丁，輔心任沖英，符天地陰六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("景休開生").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 陰宅：死杜景，符首乙丙丁，輔心任沖英，符天地陰六（坐生向死，大吉大利）
  GejuDesk(
    category: "陰宅",
    title: "陰宅",
    comment: "死杜景，符首乙丙丁，輔心任沖英，符天地陰六（坐生向死，大吉大利）",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("死杜景").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 捉人：驚，符首乙丙丁，輔心任沖，符天地六
  // "青龍返首","飛鳥跌穴"
  // "天遁","地遁","人遁","雲遁","風遁","龍遁","虎遁","神遁",
  // "天假","地假","人假","神假","鬼假","真詐","重詐","休詐",
  // "權怡","相佐"
  GejuDesk(
    category: "捉人",
    title: "捉人",
    comment: "驚，符首乙丙丁，輔心任沖，符天地六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("驚").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 打獵：傷驚死，符首乙丙丁，輔心任沖，符天地六
  // "人假","神假","鬼假"
  GejuDesk(
    category: "打獵：作戰、談判、競賽、決鬥",
    title: "打獵：作戰、談判、競賽、決鬥",
    comment: "傷驚死，符首乙丙丁，輔心任沖，符天地六",
    rule: (desk) {
      final targetTianPattern = "乙丙丁${desk.futou}";
      return desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        bool doorMatch = ("傷驚死").contains(cell.p["門"] ?? "");
        bool tianMatch =
            (targetTianPattern).contains(cell.p["天盤"] ?? "") || (targetTianPattern).contains(cell.p["寄天"] ?? "");
        return doorMatch && tianMatch;
      });
    },
  ),
  // 三勝宮（守必勝）：天乙宮、九天宮、生門宮
  GejuDesk(
    category: "三勝宮（守必勝）",
    title: "三勝宮（守必勝）",
    comment: "天乙宮、九天宮、生門宮",
    rule: (desk) {
      bool ruleForTianyi = desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        return (desk.p["值符"] as String? ?? "").contains(cell.p["星"] ?? "");
      });
      if (ruleForTianyi) return true;

      bool ruleForTian = desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        return ("天").contains(cell.p["星"] ?? ""); // Check for Star "天"
      });
      if (ruleForTian) return true;

      bool ruleForShengDoor = desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        return ("生").contains(cell.p["門"] ?? "");
      });
      return ruleForShengDoor;
    },
  ),
  // 五不擊（不可攻）：天乙宮、九天宮、生門宮、九地宮、值使宮
  GejuDesk(
    category: "五不擊（不可攻）",
    title: "五不擊（不可攻）",
    comment: "天乙宮、九天宮、生門宮、九地宮、值使宮",
    rule: (desk) {
      bool ruleForTianyi = desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        return (desk.p["值符"] as String? ?? "").contains(cell.p["星"] ?? "");
      });
      if (ruleForTianyi) return true;

      bool ruleForTianStar = desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        return ("天").contains(cell.p["星"] ?? "");
      });
      if (ruleForTianStar) return true;

      bool ruleForShengDoor = desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        return ("生").contains(cell.p["門"] ?? "");
      });
      if (ruleForShengDoor) return true;

      bool ruleForDiStar = desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        return ("地").contains(cell.p["星"] ?? "");
      });
      if (ruleForDiStar) return true;

      bool ruleForZhishiDoor = desk.cells.any((cell) {
        if (cell.luoshu == 0 || (cell.luoshu == 5 && cell.p["後天"] == "中")) return false;
        return (desk.p["值使"] as String? ?? "").contains(cell.p["門"] ?? "");
      });
      return ruleForZhishiDoor;
    },
  ),
];
