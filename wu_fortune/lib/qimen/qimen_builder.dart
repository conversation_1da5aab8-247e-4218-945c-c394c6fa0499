import 'package:wu_core/wu_extensions.dart';

import '../wu_qimen.dart';

enum DeskMode {
  yin("陰盤"),
  yang<PERSON><PERSON>("陽時盤");

  final String name;
  const DeskMode(this.name);
}

class QimenBuilder {
  static QimenDesk build({required DeskMode deskMode, DateTime? solar, int? ju}) {
    final desk = QimenDesk();

    solar ??= DateTime.now();
    ju ??= deskMode == DeskMode.yangHour ? _yangSolarHour(solar) : _yinSolarHour(solar);
    desk.p['定局'] = deskMode.name;
    desk.p['公曆'] = solar;
    desk.p['干支曆'] = GzDate.bySolar(solar);
    desk.p['局'] = ju;
    desk.p['陰陽遁'] = _calcYYDun(solar);
    desk.p['旬首'] = desk.gzdate.h.xunshou;
    desk.p['符頭'] = desk.gzdate.h.futou;
    desk.p['空亡'] = desk.gzdate.h.xunkong;
    _layDipan(desk);
    _layTianpan(desk);
    _setExecutor(desk);
    _layBashen(desk);
    _layStar(desk);
    _layDoor(desk);
    _setTianyi(desk);
    _layYingan(desk);
    _setKongwang(desk);
    _setMaxing(desk);
    return desk;
  }
} // end QimenBuilder

/// 陰盤奇門時家
int _yinSolarHour(DateTime solar) {
  final lunar = Lunar.bySolar(solar);
  var ju = (lunar.gzY.index + lunar.m + lunar.d + lunar.h + 1) % 9 + 1;
  return ju;
}

/// 陽盤奇門時家
int _yangSolarHour(DateTime solar) {
  final st = {
    "冬至": [1, 7, 4],
    "小寒": [2, 8, 5],
    "大寒": [3, 9, 6],
    "立春": [8, 5, 2],
    "雨水": [9, 6, 3],
    "驚蟄": [1, 7, 4],
    "春分": [3, 9, 6],
    "清明": [4, 1, 7],
    "穀雨": [5, 2, 8],
    "立夏": [4, 1, 7],
    "小滿": [5, 2, 8],
    "芒種": [6, 3, 9],
    "夏至": [9, 3, 6],
    "小暑": [8, 2, 5],
    "大暑": [7, 1, 4],
    "立秋": [2, 5, 8],
    "處暑": [1, 4, 7],
    "白露": [9, 3, 6],
    "秋分": [7, 1, 4],
    "寒露": [6, 9, 3],
    "霜降": [5, 8, 2],
    "立冬": [6, 9, 3],
    "小雪": [5, 8, 2],
    "大雪": [4, 7, 1],
  };
  // rule3
  final termInfo = Jieqi.bySolar(solar);
  final gzdate = GzDate.bySolar(solar);
  final idx = (gzdate.d.index ~/ 5 % 3);
  var ju = st[termInfo.name]![idx];
  return ju;
}

/// 計算陰陽遁
String _calcYYDun(DateTime solar) {
  // 夏至
  final midYear = Jieqi.byYearName(solar.year, "夏至").dateTime.ymd();
  // 冬至
  final endYear = Jieqi.byYearName(solar.year, "冬至").dateTime.ymd();
  // print("$midYear $endYear");
  // 陽遁：冬至，陰遁：夏至
  final solarYMD = solar.ymd();
  return solarYMD.compareTo(midYear) >= 0 && solarYMD.compareTo(endYear) < 0 ? "陰遁" : "陽遁";
}

/// 佈地盤
void _layDipan(QimenDesk desk) {
  var ju = desk.p['局'] as int;
  final dir = desk.p['陰陽遁'] == '陽遁' ? 1 : -1;
  for (var i = 0; i < Qimen.gans.length; i++) {
    var cell = desk.cellLuoshu(ju + dir * i);
    cell.p['地盤'] = Qimen.gans[i];
  }
  // 寄干
  final cell1 = desk.cellLuoshu(5);
  final cell2 = desk.cellLuoshu(2);
  cell2.p["寄地"] = cell1.p["地盤"];
}

/// 安天盤
void _layTianpan(QimenDesk desk) {
  // 符頭干排在時柱干之上
  var cell1 = desk.cellFirst("地盤", desk.p['符頭'], noCenter: true)!;

  var gan = desk.gzdate.h.gan.name;
  var cell2 = desk.cellFirst("地盤", gan, noCenter: true)!;
  int offset = cell2.serial - cell1.serial;
  for (var i = 0; i < Qimen.stars.length; i++) {
    final cell1 = desk.cellSerial(i);
    final cell2 = desk.cellSerial(i + offset);
    cell2.p["天盤"] = cell1.p["地盤"];
  }
  for (var i = 0; i < Qimen.stars.length; i++) {
    final cell1 = desk.cellSerial(i);
    final cell2 = desk.cellSerial(i + offset);
    cell2.p["寄天"] = cell1.p["寄地"];
  }
}

/// 定值符值使（在定地盤之後，八神九星之前）
void _setExecutor(QimenDesk desk) {
  var cell = desk.cellFirst("地盤", desk.futou, noCenter: true)!;
  desk.p['值符'] = cell.p['原星'];
  desk.p['值使'] = cell.p['原門'];
}

/// 安八神
void _layBashen(QimenDesk desk) {
  // print("*** 安八神");
  // 排完地盤奇儀，將八神之首「值符」直接放時干在地盤奇儀的宮位
  final gzdate = desk.gzdate;
  var cell = desk.cellFirst("地盤", gzdate.h.gan.name, noCenter: true);
  var serial = cell!.serial;
  // print(cell.luoshu);
  final dir = desk.p['陰陽遁'] == '陽遁' ? 1 : -1;

  for (var i = 0; i < Qimen.gods.length; i++) {
    cell = desk.cellSerial(serial + i * dir);
    cell.p['神'] = Qimen.gods.keys.toList()[i];
  }
}

/// 安九星(芮禽同宮所以只有八個)
/// 1. 先排出地盤。
/// 2. 找出直符。
/// 3. 將直符置放於地盤時干的位置。
/// 4. 以直符所落的宮位為起點，依九星排列次序。
/// 5. 若直符落入中宮，一律寄居坤二宮。
void _layStar(QimenDesk desk) {
  var cell = desk.cells.firstWhere((cell) => cell.p['神'] == '符');
  final serial = cell.serial;
  final index = (Qimen.stars * 2).indexOf(desk.p['值符']!);
  final stars = (Qimen.stars * 2).substring(index);

  for (var i = 0; i < Qimen.gods.length; i++) {
    cell = desk.cellSerial(serial + i);
    cell.p['星'] = stars[i];
  }
}

/// 安八門
/// 1. 找地盤符首宮
/// 2. 以符首支起算，陽順陰逆飛算，至時干止，中宮加一
/// 3. 以值使門起順序佈門
/// 2012/1/14 16:00 值使死門4宮
void _layDoor(QimenDesk desk) {
  final gzdate = desk.gzdate;
  final zhi = Zhi.byName(desk.xunshou[1]);
  var move = (12 + gzdate.h.zhi.index - zhi.index) % 12;
  var luoshuList = ((desk.p['陰陽遁'] == '陽遁') ? '123456789' : '987654321') * 2;
  // 符頭可以從中宮起
  var cell = desk.cellFirst('地盤', desk.futou);
  final start = luoshuList.indexOf(cell!.luoshu.toString());
  var luoshu = int.parse(luoshuList[start + move]);
  if (luoshu == 5) luoshu = 2;
  // print('${desk.p["旬首"][1]} -> ${gzdate.h.zhi.name} = $move');
  // print('$luoshuList ${cell.p['洛書']} >> $move');
  // print(luoshu);
  cell = desk.cellLuoshu(luoshu);
  final serial = cell.serial;
  final index = (Qimen.doors * 2).indexOf(desk.p['值使']!);
  final items = (Qimen.doors * 2).substring(index);
  for (var i = 0; i < 8; i++) {
    cell = desk.cellSerial(serial + i);
    cell.p['門'] = items[i];
  }
}

/// 安引干
void _layYingan(QimenDesk desk) {
  // 直接將時干加到值使門宮位。其他三奇六儀按地盤三奇六儀順序轉動排布，
  // 順時針轉和逆時針轉的結果相同
  final gzdate = desk.gzdate;
  var cell1 = desk.cellFirst('門', desk.p['值使']);

  var gan = gzdate.h.gan.name;
  var cell2 = desk.cellFirst('地盤', gan, noCenter: true);
  int offset = cell1!.serial - cell2!.serial;

  for (var i = 0; i < Qimen.stars.length; i++) {
    final cell1 = desk.cellSerial(i);
    final cell2 = desk.cellSerial(i + offset);
    cell2.p['引干'] = cell1.p['天盤'];
  }
}

/// 安天乙
void _setTianyi(QimenDesk desk) {
  final cell = desk.cellFirst('神', '符')!;
  desk.p['天乙'] = cell.p['原星'];
}

/// 安空亡
void _setKongwang(QimenDesk desk) {
  // 奇門旬空由時支的空亡所決定。
  final gzdate = desk.gzdate;
  for (var xk in gzdate.h.xunkong.split("")) {
    final cell = desk.cells.where((cell) => cell.p['地支']?.contains(xk)).first;
    // print("$xk ${cell.p['地支']} ${cell.p["後天"]}");
    cell.p['空'] = true;
  }
}

/// 安馬星
void _setMaxing(QimenDesk desk) {
  final map = {'申子辰': 8, '寅午戌': 2, '亥卯未': 4, '巳酉丑': 6};
  final gzdate = desk.gzdate;
  final zhi = gzdate.h.zhi.name;

  for (var zhis in map.keys) {
    if (zhis.contains(zhi)) {
      var cell = desk.cellLuoshu(map[zhis]!);
      cell.p['馬'] = true;
    }
  }
}
