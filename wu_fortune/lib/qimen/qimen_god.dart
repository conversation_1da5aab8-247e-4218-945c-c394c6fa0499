import '../wu_qimen.dart';

class QimenGod extends Noun {
  static const dicts = [
    ("符", "值符"),
    ("蛇", "騰蛇"),
    ("陰", "太陰"),
    ("六", "六合"),
    ("白", "白虎"),
    ("玄", "玄武"),
    ("地", "九地"),
    ("天", "九天"),
  ];

  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = dicts.map((e) => e.$1).toList();
    return _names;
  }

  String get fullname => dicts[index].$2;

  QimenGod() : super(names);
  factory QimenGod.byIndex(int index) => QimenGod()..index = index;
  factory QimenGod.byName(String name) => QimenGod()..name = name;
}
