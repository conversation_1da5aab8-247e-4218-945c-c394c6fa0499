import 'package:flutter_test/flutter_test.dart';
import 'package:wu_fortune/wu_fortune.dart';

void main() {
  group('Gan Tests', () {
    test('创建干支测试', () {
      expect(Gan.names.length, equals(10));
      expect(Gan.names, equals(['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']));
    });

    test('通过索引创建干支', () {
      final gan = Gan.byIndex(0); // 甲
      expect(gan.name, equals('甲'));
      expect(gan.index, equals(0));
    });

    test('通过名称创建干支', () {
      final gan = Gan.byName('丙');
      expect(gan.name, equals('丙'));
      expect(gan.index, equals(2));
    });

    test('五行属性测试', () {
      expect(Gan.byName('甲').wuxing.name, equals('木'));
      expect(Gan.byName('乙').wuxing.name, equals('木'));
      expect(Gan.byName('丙').wuxing.name, equals('火'));
      expect(Gan.byName('丁').wuxing.name, equals('火'));
      expect(Gan.byName('戊').wuxing.name, equals('土'));
      expect(Gan.byName('己').wuxing.name, equals('土'));
      expect(Gan.byName('庚').wuxing.name, equals('金'));
      expect(Gan.byName('辛').wuxing.name, equals('金'));
      expect(Gan.byName('壬').wuxing.name, equals('水'));
      expect(Gan.byName('癸').wuxing.name, equals('水'));
    });

    test('阴阳属性测试', () {
      // 甲丙戊庚壬为阳
      expect(Gan.byName('甲').isYang, isTrue);
      expect(Gan.byName('丙').isYang, isTrue);
      expect(Gan.byName('戊').isYang, isTrue);
      expect(Gan.byName('庚').isYang, isTrue);
      expect(Gan.byName('壬').isYang, isTrue);

      // 乙丁己辛癸为阴
      expect(Gan.byName('乙').isYang, isFalse);
      expect(Gan.byName('丁').isYang, isFalse);
      expect(Gan.byName('己').isYang, isFalse);
      expect(Gan.byName('辛').isYang, isFalse);
      expect(Gan.byName('癸').isYang, isFalse);
    });
  });
}
