import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wu_fortune/fortune/wuxing.dart';

void main() {
  group('Wuxing Tests', () {
    test('默认构造函数应该正确初始化', () {
      expect(Wuxing.names, ['木', '火', '土', '金', '水']);
    });

    test('byIndex 工厂构造函数应该正确设置索引', () {
      final wuxing = Wuxing.byIndex(0);
      expect(wuxing.name, '木');
      expect(wuxing.index, 0);

      final wuxing2 = Wuxing.byIndex(4);
      expect(wuxing2.name, '水');
      expect(wuxing2.index, 4);
    });

    test('byName 工厂构造函数应该正确设置名称', () {
      final wuxing = Wuxing.byName('木');
      expect(wuxing.name, '木');
      expect(wuxing.index, 0);

      final wuxing2 = Wuxing.byName('水');
      expect(wuxing2.name, '水');
      expect(wuxing2.index, 4);
    });

    test('color 属性应该返回正确的颜色', () {
      final wuxing = Wuxing.byName('木');
      expect(wuxing.color, Colors.green);

      final wuxing2 = Wuxing.byName('火');
      expect(wuxing2.color, Colors.red);

      final wuxing3 = Wuxing.byName('土');
      expect(wuxing3.color, Colors.brown);

      final wuxing4 = Wuxing.byName('金');
      expect(wuxing4.color, Colors.amber);

      final wuxing5 = Wuxing.byName('水');
      expect(wuxing5.color, Colors.black);
    });

    test('无效名称应该抛出异常', () {
      expect(() => Wuxing.byName('shui'), throwsArgumentError);
    });
  });
}
