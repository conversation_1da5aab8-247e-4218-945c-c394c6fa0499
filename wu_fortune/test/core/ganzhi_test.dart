import 'package:flutter_test/flutter_test.dart';
import 'package:wu_fortune/wu_fortune.dart';

void main() {
  group('GanZhi Tests', () {
    test('创建干支组合测试', () {
      final ganzhi = GanZhi.byName("甲子");
      expect(ganzhi.toString(), equals('甲子'));
      expect(ganzhi.gan.name, equals('甲'));
      expect(ganzhi.zhi.name, equals('子'));
    });

    test('通过索引创建干支', () {
      final ganzhi = GanZhi.byIndex(0); // 应该是甲子
      expect(ganzhi.toString(), equals('甲子'));
      expect(ganzhi.index, equals(0));
    });

    test('干支配合测试', () {
      // 测试合法的干支配合
      expect(() => GanZhi.byName("甲子"), returnsNormally);

      // 测试特定干支组合的五行属性
      final ganzhi = GanZhi.byName("甲寅");
      expect(ganzhi.gan.wuxing.name, equals('木'));
      expect(ganzhi.zhi.wuxing.name, equals('木'));
    });
  });
}
