import 'package:flutter_test/flutter_test.dart';
import 'package:wu_fortune/wu_fortune.dart';

void main() {
  group('Zhi Tests', () {
    test('创建地支测试', () {
      expect(Zhi.names.length, equals(12));
      expect(Zhi.names, equals(['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']));
    });

    test('通过索引创建地支', () {
      final zhi = Zhi.byIndex(0); // 子
      expect(zhi.name, equals('子'));
      expect(zhi.index, equals(0));
    });

    test('通过名称创建地支', () {
      final zhi = Zhi.byName('寅');
      expect(zhi.name, equals('寅'));
      expect(zhi.index, equals(2));
    });

    test('五行属性测试', () {
      expect(Zhi.byName('子').wuxing.name, equals('水'));
      expect(Zhi.byName('丑').wuxing.name, equals('土'));
      expect(Zhi.byName('寅').wuxing.name, equals('木'));
      expect(Zhi.byName('卯').wuxing.name, equals('木'));
      expect(Zhi.byName('辰').wuxing.name, equals('土'));
      expect(Zhi.byName('巳').wuxing.name, equals('火'));
      expect(Zhi.byName('午').wuxing.name, equals('火'));
      expect(Zhi.byName('未').wuxing.name, equals('土'));
      expect(Zhi.byName('申').wuxing.name, equals('金'));
      expect(Zhi.byName('酉').wuxing.name, equals('金'));
      expect(Zhi.byName('戌').wuxing.name, equals('土'));
      expect(Zhi.byName('亥').wuxing.name, equals('水'));
    });

    test('阴阳属性测试', () {
      // 子、寅、辰、午、申、戌为阳
      expect(Zhi.byName('子').isYang, isTrue);
      expect(Zhi.byName('寅').isYang, isTrue);
      expect(Zhi.byName('辰').isYang, isTrue);
      expect(Zhi.byName('午').isYang, isTrue);
      expect(Zhi.byName('申').isYang, isTrue);
      expect(Zhi.byName('戌').isYang, isTrue);

      // 丑、卯、巳、未、酉、亥为阴
      expect(Zhi.byName('丑').isYang, isFalse);
      expect(Zhi.byName('卯').isYang, isFalse);
      expect(Zhi.byName('巳').isYang, isFalse);
      expect(Zhi.byName('未').isYang, isFalse);
      expect(Zhi.byName('酉').isYang, isFalse);
      expect(Zhi.byName('亥').isYang, isFalse);
    });
  });
}
