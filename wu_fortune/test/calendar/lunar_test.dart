import 'package:flutter_test/flutter_test.dart';
import 'package:wu_fortune/calendar/lunar.dart';
import 'package:wu_fortune/wu_fortune.dart';

void main() {
  group('Lunar', () {
    test('建立 Lunar 物件', () {
      final lunar = Lunar(y: 2023, m: 5, d: 15, h: 12, isLeap: false);

      expect(lunar.y, equals(2023));
      expect(lunar.m, equals(5));
      expect(lunar.d, equals(15));
      expect(lunar.h, equals(12));
      expect(lunar.isLeap, equals(false));
    });

    test('從陽曆轉換為陰曆', () {
      final dateTime = DateTime(2023, 6, 22, 14, 30); // 2023年6月22日14:30
      final lunar = Lunar.bySolar(dateTime);

      // 注意：這裡的期望值需要根據實際的農曆日期進行調整
      // 以下僅為示例，實際測試時需要確認正確的農曆日期
      expect(lunar.y, isNotNull);
      expect(lunar.m, isNotNull);
      expect(lunar.d, isNotNull);
      expect(lunar.h, isNotNull);
      expect(lunar.isLeap, isNotNull);
    });

    test('測試 gzY getter', () {
      final lunar = Lunar(y: 2023, m: 5, d: 15, h: 12, isLeap: false);
      // 2023年對應的地支，需要根據實際情況調整期望值
      expect(lunar.gzY, isA<Zhi>());
    });

    test('測試 zhY getter', () {
      final lunar = Lunar(y: 2023, m: 5, d: 15, h: 12, isLeap: false);
      // 2023年對應的生肖，需要根據實際情況調整期望值
      expect(lunar.zhY, isNotEmpty);
    });

    test('測試 zhM getter', () {
      final lunar = Lunar(y: 2023, m: 5, d: 15, h: 12, isLeap: false);
      // 農曆5月的中文表示，需要根據實際情況調整期望值
      expect(lunar.zhM, isNotEmpty);
    });

    test('測試 zhD getter', () {
      final lunar = Lunar(y: 2023, m: 5, d: 15, h: 12, isLeap: false);
      // 農曆15日的中文表示，需要根據實際情況調整期望值
      expect(lunar.zhD, isNotEmpty);
    });

    test('測試 toString 方法', () {
      final lunar = Lunar(y: 2023, m: 5, d: 15, h: 12, isLeap: false);
      final result = lunar.toString();

      expect(result, isNotEmpty);
      expect(result, contains('年'));
      expect(result, contains('月'));
      expect(result, contains('日'));
      expect(result, contains('時'));
    });

    test('測試閏月的 toString 方法', () {
      final lunar = Lunar(y: 2023, m: 5, d: 15, h: 12, isLeap: true);
      final result = lunar.toString();

      expect(result, contains('閏'));
    });
  });
}
